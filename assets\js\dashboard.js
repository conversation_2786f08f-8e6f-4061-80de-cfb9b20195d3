/**
 * JavaScript للوحة التحكم - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.3
 * @date 2025-06-17
 */

// إعدادات الألوان
const colors = {
    primary: '#667eea',
    secondary: '#764ba2',
    success: '#28a745',
    warning: '#ffc107',
    danger: '#dc3545',
    info: '#17a2b8',
    light: '#f8f9fa',
    dark: '#2c3e50'
};

const chartColors = [
    '#667eea', '#764ba2', '#f093fb', '#f5576c',
    '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
    '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
];

// إعدادات Chart.js العامة
Chart.defaults.font.family = 'Tajawal, Arial, sans-serif';
Chart.defaults.font.size = 12;

// مخطط توزيع الطلبات
function createRequestsDistributionChart() {
    const ctx = document.getElementById('requestsDistributionChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['طلبات المحاسبة', 'طلبات إنهاء النزاع', 'طلبات تسوية النزاع'],
            datasets: [{
                data: [
                    chartData.requests.accounting,
                    chartData.requests.conflict,
                    chartData.requests.dispute
                ],
                backgroundColor: [chartColors[0], chartColors[1], chartColors[2]],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed.toLocaleString() + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });
}

// مخطط الطلبات الشهرية
function createMonthlyRequestsChart() {
    const ctx = document.getElementById('monthlyRequestsChart').getContext('2d');
    
    const labels = chartData.monthly.map(item => item.month).reverse();
    const data = chartData.monthly.map(item => item.count).reverse();
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'عدد الطلبات',
                data: data,
                borderColor: chartColors[0],
                backgroundColor: chartColors[0] + '20',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: chartColors[0],
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'عدد الطلبات: ' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

// مخطط المناطق
function createRegionsChart() {
    const ctx = document.getElementById('regionsChart').getContext('2d');
    
    const labels = chartData.regions.map(item => item.region);
    const data = chartData.regions.map(item => item.count);
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'عدد المأموريات',
                data: data,
                backgroundColor: chartColors.slice(0, labels.length),
                borderColor: chartColors.slice(0, labels.length),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'عدد المأموريات: ' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString();
                        }
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45,
                        minRotation: 45
                    }
                }
            }
        }
    });
}

// مخطط مديري العموم
function createManagersChart() {
    const ctx = document.getElementById('managersChart').getContext('2d');
    
    const labels = chartData.managers.map(item => item.general_manager);
    const data = chartData.managers.map(item => item.count);
    
    new Chart(ctx, {
        type: 'horizontalBar',
        data: {
            labels: labels,
            datasets: [{
                label: 'عدد المأموريات',
                data: data,
                backgroundColor: chartColors[1],
                borderColor: chartColors[1],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'عدد المأموريات: ' + context.parsed.x.toLocaleString();
                        }
                    }
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

// وضع الليل/النهار
function toggleTheme() {
    const body = document.body;
    const themeToggle = document.getElementById('themeToggle');
    
    body.classList.toggle('dark-mode');
    
    if (body.classList.contains('dark-mode')) {
        themeToggle.textContent = '☀️';
        localStorage.setItem('theme', 'dark');
    } else {
        themeToggle.textContent = '🌙';
        localStorage.setItem('theme', 'light');
    }
}

// تحميل الثيم المحفوظ
function loadSavedTheme() {
    const savedTheme = localStorage.getItem('theme');
    const body = document.body;
    const themeToggle = document.getElementById('themeToggle');
    
    if (savedTheme === 'dark') {
        body.classList.add('dark-mode');
        themeToggle.textContent = '☀️';
    } else {
        themeToggle.textContent = '🌙';
    }
}

// تحديث المخططات عند تغيير الثيم
function updateChartsTheme() {
    const isDark = document.body.classList.contains('dark-mode');
    const textColor = isDark ? '#ecf0f1' : '#2c3e50';
    const gridColor = isDark ? '#4a5f7a' : '#e9ecef';
    
    Chart.defaults.color = textColor;
    Chart.defaults.borderColor = gridColor;
    Chart.defaults.backgroundColor = isDark ? '#34495e' : '#fff';
}

// تحديث الوقت الحقيقي (كل 30 ثانية)
function updateRealTimeData() {
    // يمكن إضافة AJAX هنا لتحديث البيانات
    console.log('تحديث البيانات...');
}

// تهيئة لوحة التحكم
function initDashboard() {
    // تحميل الثيم المحفوظ
    loadSavedTheme();
    
    // إنشاء المخططات
    createRequestsDistributionChart();
    createMonthlyRequestsChart();
    createRegionsChart();
    createManagersChart();
    
    // تحديث دوري للبيانات (كل 5 دقائق)
    setInterval(updateRealTimeData, 300000);
    
    // إضافة مستمع لتغيير الثيم
    document.addEventListener('DOMContentLoaded', function() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'class') {
                    updateChartsTheme();
                }
            });
        });
        
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });
    });
}

// تشغيل لوحة التحكم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initDashboard);

// إضافة تأثيرات تفاعلية
document.addEventListener('DOMContentLoaded', function() {
    // تأثير الظهور التدريجي للبطاقات
    const cards = document.querySelectorAll('.kpi-card, .chart-container');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    });
    
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});

// دوال مساعدة للتصدير
function exportChartAsPNG(chartId, filename) {
    const canvas = document.getElementById(chartId);
    const url = canvas.toDataURL('image/png');
    const link = document.createElement('a');
    link.download = filename + '.png';
    link.href = url;
    link.click();
}

function printDashboard() {
    window.print();
}
