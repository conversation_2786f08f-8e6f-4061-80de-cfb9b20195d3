<?php
/**
 * أداة إصلاح ملفات CSV - TIaF Report System
 * إزالة BOM وتنظيف أسماء الأعمدة
 *
 * <AUTHOR> Development Team
 * @version 1.4.1
 * @date 2025-06-17
 */

// بدء الجلسة في بداية الملف
session_start();

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح ملفات CSV - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
        }
        
        .file-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح ملفات CSV</h1>
            <p>أداة لإزالة BOM وتنظيف أسماء الأعمدة في ملفات CSV</p>
        </div>
        
        <div class="content">
            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
                $file = $_FILES['csv_file'];
                
                if ($file['error'] === UPLOAD_ERR_OK) {
                    echo "<div class='section'>";
                    echo "<h3>🔄 معالجة الملف</h3>";
                    
                    // قراءة الملف الأصلي
                    $originalContent = file_get_contents($file['tmp_name']);
                    
                    // إزالة BOM
                    $cleanContent = str_replace("\xEF\xBB\xBF", '', $originalContent);
                    
                    // تحليل CSV
                    $lines = explode("\n", $cleanContent);
                    $headers = str_getcsv($lines[0]);
                    
                    echo "<h4>📋 معلومات الملف الأصلي:</h4>";
                    echo "<div class='file-info'>";
                    echo "<p><strong>اسم الملف:</strong> " . htmlspecialchars($file['name']) . "</p>";
                    echo "<p><strong>الحجم:</strong> " . number_format($file['size']) . " بايت</p>";
                    echo "<p><strong>عدد الأسطر:</strong> " . count($lines) . "</p>";
                    echo "<p><strong>عدد الأعمدة:</strong> " . count($headers) . "</p>";
                    echo "<p><strong>يحتوي على BOM:</strong> " . (strpos($originalContent, "\xEF\xBB\xBF") !== false ? 'نعم ❌' : 'لا ✅') . "</p>";
                    echo "</div>";
                    
                    // عرض أسماء الأعمدة قبل وبعد التنظيف
                    echo "<h4>🔍 أسماء الأعمدة:</h4>";
                    echo "<table style='width: 100%; border-collapse: collapse;'>";
                    echo "<tr style='background: #f8f9fa;'><th style='border: 1px solid #ddd; padding: 8px;'>الرقم</th><th style='border: 1px solid #ddd; padding: 8px;'>قبل التنظيف</th><th style='border: 1px solid #ddd; padding: 8px;'>بعد التنظيف</th></tr>";
                    
                    $cleanHeaders = [];
                    foreach ($headers as $index => $header) {
                        $cleanHeader = trim(str_replace("\xEF\xBB\xBF", '', $header));
                        $cleanHeaders[] = $cleanHeader;
                        
                        echo "<tr>";
                        echo "<td style='border: 1px solid #ddd; padding: 8px; text-align: center;'>" . ($index + 1) . "</td>";
                        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($header) . "</td>";
                        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($cleanHeader) . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                    
                    // إنشاء الملف المُصحح
                    $fixedLines = [];
                    $fixedLines[] = implode(',', array_map(function($header) {
                        return '"' . str_replace('"', '""', $header) . '"';
                    }, $cleanHeaders));
                    
                    // إضافة باقي الأسطر
                    for ($i = 1; $i < count($lines); $i++) {
                        if (trim($lines[$i]) !== '') {
                            $fixedLines[] = $lines[$i];
                        }
                    }
                    
                    $fixedContent = implode("\n", $fixedLines);
                    
                    // حفظ الملف المُصحح
                    $fixedFileName = 'fixed_' . $file['name'];
                    $fixedFilePath = sys_get_temp_dir() . '/' . $fixedFileName;

                    // التأكد من إمكانية الكتابة
                    if (file_put_contents($fixedFilePath, $fixedContent) === false) {
                        echo "<div class='section error'>";
                        echo "<h4>❌ خطأ في حفظ الملف</h4>";
                        echo "<p>لا يمكن حفظ الملف المُصحح. تحقق من صلاحيات المجلد المؤقت.</p>";
                        echo "</div>";
                        return;
                    }
                    
                    echo "<div class='section success'>";
                    echo "<h4>✅ تم إصلاح الملف بنجاح!</h4>";
                    echo "<p><strong>الملف الجديد:</strong> $fixedFileName</p>";
                    echo "<p><strong>الحجم الجديد:</strong> " . number_format(strlen($fixedContent)) . " بايت</p>";
                    echo "<p><strong>التغييرات:</strong></p>";
                    echo "<ul>";
                    echo "<li>تم إزالة BOM (Byte Order Mark)</li>";
                    echo "<li>تم تنظيف أسماء الأعمدة</li>";
                    echo "<li>تم إزالة الأسطر الفارغة</li>";
                    echo "</ul>";
                    
                    // رابط التحميل
                    echo "<a href='download_fixed.php?file=" . urlencode($fixedFileName) . "' class='btn'>📥 تحميل الملف المُصحح</a>";
                    echo "</div>";
                    
                    // حفظ مسار الملف في الجلسة للتحميل
                    if (!isset($_SESSION['fixed_files'])) {
                        $_SESSION['fixed_files'] = [];
                    }
                    $_SESSION['fixed_files'][$fixedFileName] = $fixedFilePath;
                    
                    echo "</div>";
                } else {
                    echo "<div class='section error'>";
                    echo "<h3>❌ خطأ في رفع الملف</h3>";
                    echo "<p>كود الخطأ: " . $file['error'] . "</p>";
                    echo "</div>";
                }
            }
            ?>
            
            <!-- نموذج رفع الملف -->
            <div class="section">
                <h3>📤 رفع ملف CSV للإصلاح</h3>
                <p>ارفع ملف CSV لإزالة BOM وتنظيف أسماء الأعمدة:</p>
                
                <form method="post" enctype="multipart/form-data">
                    <div class="upload-area">
                        <p>📁 اختر ملف CSV</p>
                        <input type="file" name="csv_file" accept=".csv" required>
                    </div>
                    <button type="submit" class="btn">🔧 إصلاح الملف</button>
                </form>
            </div>
            
            <!-- معلومات مفيدة -->
            <div class="section warning">
                <h3>💡 معلومات مفيدة</h3>
                <h4>ما هو BOM؟</h4>
                <p>BOM (Byte Order Mark) هو تسلسل من البايتات يُضاف في بداية ملفات النص لتحديد ترميز الملف. يمكن أن يسبب مشاكل في استيراد البيانات.</p>
                
                <h4>متى تحتاج لاستخدام هذه الأداة؟</h4>
                <ul>
                    <li>عندما تظهر رسائل خطأ مثل "Unknown column '﻿رقم الطلب'"</li>
                    <li>عندما تجد رموز غريبة في بداية أسماء الأعمدة</li>
                    <li>عندما يفشل استيراد ملف CSV بدون سبب واضح</li>
                </ul>
                
                <h4>نصائح:</h4>
                <ul>
                    <li>احفظ ملفات CSV بترميز UTF-8 بدون BOM</li>
                    <li>تجنب المسافات الزائدة في أسماء الأعمدة</li>
                    <li>استخدم محرر نصوص متقدم مثل Notepad++</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="import_data.php" class="btn">استيراد البيانات</a>
                <a href="debug_import.php" class="btn">تشخيص الاستيراد</a>
            </div>
        </div>
    </div>
</body>
</html>
