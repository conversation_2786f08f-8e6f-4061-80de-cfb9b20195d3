<?php
/**
 * إعادة إنشاء الجداول المساعدة - TIaF Report System
 * إعادة إنشاء الجداول المرجعية طبقاً لملفات Mapping Table
 * 
 * <AUTHOR> Development Team
 * @version 1.1
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔄 إعادة إنشاء الجداول المساعدة - TIaF Report System</h2>";
    echo "<p><strong>ملاحظة:</strong> سيتم حذف الجداول المساعدة الحالية وإعادة إنشاؤها طبقاً لملفات Mapping Table</p>";
    
    // ===== حذف الجداول المساعدة الحالية =====
    echo "<h3>🗑️ حذف الجداول المساعدة الحالية</h3>";
    
    $tables_to_drop = ['classification', 'container', 'request_status'];
    foreach ($tables_to_drop as $table) {
        try {
            $pdo->exec("DROP TABLE IF EXISTS `$table`");
            echo "<p>✅ تم حذف جدول $table</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ تحذير: لم يتم العثور على جدول $table</p>";
        }
    }
    
    // ===== إعادة إنشاء الجداول المساعدة =====
    echo "<h3>🏗️ إعادة إنشاء الجداول المساعدة</h3>";
    
    // جدول التصنيف (Classification) - 7 حقول كما هو في ملف CSV
    $sql_classification = "
    CREATE TABLE `classification` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `classification_type` varchar(50) NOT NULL COMMENT 'التصنيف',
        `office_code` varchar(10) NOT NULL COMMENT 'كود المأمورية',
        `office_name` varchar(255) NOT NULL COMMENT 'اسم المأمورية',
        `region` varchar(100) NOT NULL COMMENT 'المنطقة',
        `office_branch` varchar(255) NOT NULL COMMENT 'المامورية',
        `specialization` varchar(100) NOT NULL COMMENT 'الاختصاص',
        `general_manager` varchar(100) NOT NULL COMMENT 'مديري العموم',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_office_code` (`office_code`),
        KEY `idx_classification_type` (`classification_type`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_classification);
    echo "<p>✅ تم إنشاء جدول التصنيف (classification) - 7 حقول</p>";
    
    // جدول الوعاء (Container) - 3 حقول كما هو في ملف CSV
    $sql_container = "
    CREATE TABLE `container` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `container_code` varchar(10) NOT NULL COMMENT 'الوعاء',
        `container_name` varchar(255) NOT NULL COMMENT 'اسم الوعاء',
        `classification` varchar(50) NOT NULL COMMENT 'التصنيف',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_container_code` (`container_code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_container);
    echo "<p>✅ تم إنشاء جدول الوعاء (container) - 3 حقول</p>";
    
    // جدول حالة الطلب (Request Status) - 2 حقل كما هو في ملف CSV
    $sql_request_status = "
    CREATE TABLE `request_status` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `status_code` varchar(10) NOT NULL COMMENT 'حالة الطلب',
        `status_description` varchar(255) NOT NULL COMMENT 'وصف الحالة',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_status_code` (`status_code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_request_status);
    echo "<p>✅ تم إنشاء جدول حالة الطلب (request_status) - 2 حقل</p>";
    
    // ===== استيراد البيانات من ملفات CSV =====
    echo "<h3>📥 استيراد البيانات من ملفات Mapping Table</h3>";
    
    // استيراد بيانات التصنيف من ملف CSV
    $classification_file = 'Mapping Table/جدول التصنيف.csv';
    if (file_exists($classification_file)) {
        $handle = fopen($classification_file, 'r');
        $headers = fgetcsv($handle); // تخطي العنوان الأول
        
        $stmt = $pdo->prepare("INSERT INTO classification (classification_type, office_code, office_name, region, office_branch, specialization, general_manager) VALUES (?, ?, ?, ?, ?, ?, ?)");
        
        $count = 0;
        while (($data = fgetcsv($handle)) !== FALSE) {
            if (count($data) >= 7 && !empty(trim($data[0]))) {
                // تنظيف البيانات
                $cleanData = array_map('trim', $data);
                $stmt->execute($cleanData);
                $count++;
            }
        }
        fclose($handle);
        echo "<p>✅ تم إدخال $count سجل من بيانات التصنيف</p>";
    } else {
        echo "<p>❌ ملف التصنيف غير موجود: $classification_file</p>";
    }
    
    // استيراد بيانات الوعاء من ملف CSV
    $container_file = 'Mapping Table/جدول الوعاء.csv';
    if (file_exists($container_file)) {
        $handle = fopen($container_file, 'r');
        $headers = fgetcsv($handle); // تخطي العنوان الأول
        
        $stmt = $pdo->prepare("INSERT INTO container (container_code, container_name, classification) VALUES (?, ?, ?)");
        
        $count = 0;
        while (($data = fgetcsv($handle)) !== FALSE) {
            if (count($data) >= 3 && !empty(trim($data[0]))) {
                // تنظيف البيانات
                $cleanData = array_map('trim', $data);
                $stmt->execute($cleanData);
                $count++;
            }
        }
        fclose($handle);
        echo "<p>✅ تم إدخال $count سجل من بيانات الوعاء</p>";
    } else {
        echo "<p>❌ ملف الوعاء غير موجود: $container_file</p>";
    }
    
    // استيراد بيانات حالة الطلب من ملف CSV
    $status_file = 'Mapping Table/جدول حالة الطلب.csv';
    if (file_exists($status_file)) {
        $handle = fopen($status_file, 'r');
        $headers = fgetcsv($handle); // تخطي العنوان الأول
        
        $stmt = $pdo->prepare("INSERT INTO request_status (status_code, status_description) VALUES (?, ?)");
        
        $count = 0;
        while (($data = fgetcsv($handle)) !== FALSE) {
            if (count($data) >= 2 && !empty(trim($data[0]))) {
                // تنظيف البيانات
                $cleanData = array_map('trim', $data);
                $stmt->execute([$cleanData[0], $cleanData[1]]);
                $count++;
            }
        }
        fclose($handle);
        echo "<p>✅ تم إدخال $count سجل من بيانات حالة الطلب</p>";
    } else {
        echo "<p>❌ ملف حالة الطلب غير موجود: $status_file</p>";
    }
    
    // ===== عرض ملخص النتائج =====
    echo "<h3>📊 ملخص النتائج</h3>";
    
    // عدد السجلات في كل جدول
    $tables_info = [
        'classification' => 'التصنيف',
        'container' => 'الوعاء', 
        'request_status' => 'حالة الطلب'
    ];
    
    foreach ($tables_info as $table => $arabic_name) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
            $count = $stmt->fetchColumn();
            echo "<p>📋 جدول $arabic_name ($table): $count سجل</p>";
        } catch (Exception $e) {
            echo "<p>❌ خطأ في جدول $table: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>🎉 تم إعادة إنشاء الجداول المساعدة بنجاح!</h4>";
    echo "<ul>";
    echo "<li>✅ جدول التصنيف: 7 حقول كما هو في ملف CSV</li>";
    echo "<li>✅ جدول الوعاء: 3 حقول كما هو في ملف CSV</li>";
    echo "<li>✅ جدول حالة الطلب: 2 حقل كما هو في ملف CSV</li>";
    echo "<li>✅ تم استيراد جميع البيانات من ملفات Mapping Table</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='index.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة للصفحة الرئيسية</a></p>";
    echo "<p><a href='import_data.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>استيراد البيانات الأساسية</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    die();
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
    die();
}
?>
