# دليل استيراد ملفات CSV - TIaF Report System

## نظرة عامة

هذا الدليل يوضح كيفية حل مشاكل استيراد ملفات CSV الشائعة في نظام TIaF Report System.

## المشاكل الشائعة

### 1. مشكلة BOM (Byte Order Mark)

**الأعراض:**
```
❌ خطأ في السطر 1: Unknown column '﻿رقم الطلب' in 'field list'
```

**السبب:**
- ملف CSV يحتوي على BOM في بداية الملف
- BOM هو تسلسل من البايتات (`\xEF\xBB\xBF`) يُضاف تلقائياً ببعض المحررات

**الحل:**
استخدم أداة إصلاح CSV: `fix_csv_simple.php`

### 2. مشكلة أسماء الأعمدة

**الأعراض:**
```
❌ خطأ في السطر 1: Unknown column 'رقم الطلب' in 'field list'
```

**السبب:**
- أسماء الأعمدة في CSV عربية
- أسماء الحقول في قاعدة البيانات إنجليزية

**الحل:**
النظام يحول الأسماء تلقائياً باستخدام جدول التحويل

## الأدوات المتاحة

### 1. أداة التشخيص (`debug_import.php`)

**الوظائف:**
- عرض هيكل قاعدة البيانات
- تحليل ملفات CSV
- عرض جدول تحويل الأسماء
- التحقق من التوافق

**كيفية الاستخدام:**
1. اذهب إلى `debug_import.php`
2. ارفع ملف CSV للتحليل
3. راجع التقرير المفصل

### 2. أداة إصلاح CSV (`fix_csv_simple.php`)

**الوظائف:**
- إزالة BOM من الملفات
- تنظيف أسماء الأعمدة
- إزالة الأسطر الفارغة
- تحميل الملف المُصحح

**كيفية الاستخدام:**
1. اذهب إلى `fix_csv_simple.php`
2. ارفع ملف CSV المُشكِل
3. حمل الملف المُصحح
4. استورد الملف المُصحح

### 3. أداة الاستيراد المحسنة (`import_data.php`)

**المميزات الجديدة:**
- معالجة BOM تلقائياً
- تحويل أسماء الأعمدة تلقائياً
- رسائل خطأ أوضح
- عرض تقدم العملية

## جدول تحويل أسماء الأعمدة

| الاسم العربي | اسم الحقل الإنجليزي |
|-------------|-------------------|
| رقم الطلب | request_number |
| تاريخ الإنشاء | creation_date |
| منشئ الطلب | created_by |
| تاريخ التعديل | modification_date |
| معدل الطلب | modified_by |
| حالة الطلب | request_status |
| وصف الحالة | status_description |
| نوع الطلب | request_type |
| اسم نوع الطلب | request_type_name |
| رقم التسجيل الضريبي | tax_registration_number |
| كود المأمورية | office_code |
| اسم المأمورية | office_name |
| اسم المكلف | taxpayer_name |
| العنوان | address |
| رقم الهاتف | phone_number |
| البريد الإلكتروني | email |
| نوع المحاسبة | accounting_type |
| وصف نوع المحاسبة | accounting_type_description |
| الوعاء | container |
| اسم الوعاء | container_name |
| الفترة | period |
| مرحلة النزاع | dispute_stage |
| وصف مرحلة النزاع | dispute_stage_description |
| رقم القضية | case_number |
| جهة النزاع | dispute_authority |
| اسم جهة النزاع | dispute_authority_name |
| جهة أخرى | other_authority |
| الضريبة طبقاً للإقرار | tax_according_to_declaration |
| الضريبة من آخر ربط | tax_from_last_assessment |
| سنة آخر ربط | last_assessment_year |
| الضريبة طبقاً للنموذج | tax_according_to_form |
| الضريبة المسددة | tax_paid |
| الضريبة المتوقعة | expected_tax |
| الضريبة طبقاً للقانون | tax_according_to_law |
| الضريبة المستحقة | tax_due_payment |

## خطوات حل المشاكل

### الخطوة 1: تشخيص المشكلة
```
1. اذهب إلى debug_import.php
2. ارفع ملف CSV المُشكِل
3. راجع التقرير لتحديد نوع المشكلة
```

### الخطوة 2: إصلاح الملف (إذا لزم الأمر)
```
1. اذهب إلى fix_csv_simple.php
2. ارفع ملف CSV
3. حمل الملف المُصحح
```

### الخطوة 3: الاستيراد
```
1. اذهب إلى import_data.php
2. ارفع الملف (الأصلي أو المُصحح)
3. اختر الجدول المناسب
4. ابدأ الاستيراد
```

## أمثلة عملية

### مثال 1: ملف يحتوي على BOM

**المشكلة:**
```
❌ خطأ في السطر 1: Unknown column '﻿رقم الطلب'
```

**الحل:**
1. استخدم `fix_csv_simple.php`
2. ارفع الملف
3. حمل الملف المُصحح
4. استورد الملف المُصحح

### مثال 2: أسماء أعمدة غير متطابقة

**المشكلة:**
```
❌ خطأ في السطر 1: Unknown column 'Request_Number'
```

**الحل:**
1. تأكد من أن أسماء الأعمدة موجودة في جدول التحويل
2. أو أضف التحويل الجديد في `getColumnMapping()`

### مثال 3: ملف فارغ أو تالف

**المشكلة:**
```
❌ ملف CSV فارغ أو تالف
```

**الحل:**
1. تحقق من أن الملف يحتوي على بيانات
2. تأكد من أن الملف بتنسيق CSV صحيح
3. تحقق من ترميز الملف (UTF-8)

## نصائح لتجنب المشاكل

### 1. إعداد ملفات CSV
- احفظ الملفات بترميز UTF-8 بدون BOM
- تجنب المسافات الزائدة في أسماء الأعمدة
- استخدم أسماء الأعمدة العربية المحددة في جدول التحويل

### 2. استخدام المحررات
- **Notepad++**: اختر "UTF-8" بدلاً من "UTF-8-BOM"
- **Excel**: احفظ كـ "CSV UTF-8 (Comma delimited)"
- **LibreOffice**: اختر "UTF-8" في خيارات الحفظ

### 3. التحقق من الملفات
- استخدم `debug_import.php` لفحص الملفات قبل الاستيراد
- تحقق من أن عدد الأعمدة ثابت في جميع الأسطر
- تأكد من عدم وجود أسطر فارغة في وسط الملف

## الدعم الفني

إذا واجهت مشاكل أخرى:

1. **استخدم أداة التشخيص** للحصول على تقرير مفصل
2. **راجع رسائل الخطأ** في صفحة الاستيراد
3. **تحقق من هيكل قاعدة البيانات** باستخدام `test_system.php`
4. **راجع ملف CHANGELOG.md** للتحديثات الأخيرة

---

**ملاحظة:** هذا الدليل يغطي المشاكل الشائعة. للمشاكل المتقدمة، راجع ملفات التوثيق الأخرى أو استخدم أدوات التشخيص المتاحة.
