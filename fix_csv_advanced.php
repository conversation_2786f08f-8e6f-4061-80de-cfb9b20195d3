<?php
/**
 * أداة إصلاح ملفات CSV المتقدمة - TIaF Report System
 * إزالة BOM وتنظيف أسماء الأعمدة (باستخدام ملفات مؤقتة)
 * 
 * <AUTHOR> Development Team
 * @version 1.4.2
 * @date 2025-06-17
 */

// معالجة التحميل
if (isset($_GET['download']) && isset($_GET['file'])) {
    $fileName = basename($_GET['file']);
    $filePath = sys_get_temp_dir() . '/tiaf_fixed_' . $fileName;
    
    if (file_exists($filePath)) {
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $fileName . '"');
        header('Content-Length: ' . filesize($filePath));
        header('Cache-Control: private, max-age=0, must-revalidate');
        header('Pragma: public');
        
        readfile($filePath);
        
        // حذف الملف المؤقت بعد التحميل
        unlink($filePath);
        exit;
    } else {
        die('الملف غير موجود أو انتهت صلاحيته');
    }
}

// معالجة رفع الملف
$processed = false;
$originalFileName = '';
$fixedFileName = '';
$fileInfo = [];
$downloadLink = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    $file = $_FILES['csv_file'];
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        $processed = true;
        $originalFileName = $file['name'];
        
        // قراءة الملف الأصلي
        $originalContent = file_get_contents($file['tmp_name']);
        
        // إزالة BOM
        $cleanContent = str_replace("\xEF\xBB\xBF", '', $originalContent);
        
        // تحليل CSV
        $lines = explode("\n", $cleanContent);
        $headers = str_getcsv($lines[0]);
        
        // معلومات الملف
        $fileInfo = [
            'name' => $originalFileName,
            'size' => $file['size'],
            'lines' => count($lines),
            'columns' => count($headers),
            'has_bom' => strpos($originalContent, "\xEF\xBB\xBF") !== false
        ];
        
        // تنظيف أسماء الأعمدة
        $cleanHeaders = [];
        foreach ($headers as $header) {
            $cleanHeader = trim(str_replace("\xEF\xBB\xBF", '', $header));
            $cleanHeaders[] = $cleanHeader;
        }
        
        // إنشاء الملف المُصحح
        $fixedLines = [];
        $fixedLines[] = implode(',', array_map(function($header) {
            return '"' . str_replace('"', '""', $header) . '"';
        }, $cleanHeaders));
        
        // إضافة باقي الأسطر
        for ($i = 1; $i < count($lines); $i++) {
            if (trim($lines[$i]) !== '') {
                $fixedLines[] = $lines[$i];
            }
        }
        
        $fixedContent = implode("\n", $fixedLines);
        $fixedFileName = 'fixed_' . $originalFileName;
        
        // حفظ الملف المُصحح في مجلد مؤقت
        $tempFilePath = sys_get_temp_dir() . '/tiaf_fixed_' . $fixedFileName;
        if (file_put_contents($tempFilePath, $fixedContent) !== false) {
            $downloadLink = '?download=1&file=' . urlencode($fixedFileName);
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح ملفات CSV المتقدم - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #5a6fd8;
            background: #f8f9ff;
        }
        
        .file-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 0.9rem;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح ملفات CSV المتقدم</h1>
            <p>أداة متقدمة لإزالة BOM وتنظيف أسماء الأعمدة في ملفات CSV</p>
        </div>
        
        <div class="content">
            <?php if ($processed): ?>
                <div class="section">
                    <h3>🔄 نتائج المعالجة</h3>
                    
                    <h4>📋 معلومات الملف الأصلي:</h4>
                    <div class="file-info">
                        <p><strong>اسم الملف:</strong> <?php echo htmlspecialchars($fileInfo['name']); ?></p>
                        <p><strong>الحجم:</strong> <?php echo number_format($fileInfo['size']); ?> بايت</p>
                        <p><strong>عدد الأسطر:</strong> <?php echo number_format($fileInfo['lines']); ?></p>
                        <p><strong>عدد الأعمدة:</strong> <?php echo $fileInfo['columns']; ?></p>
                        <p><strong>يحتوي على BOM:</strong> <?php echo $fileInfo['has_bom'] ? 'نعم ❌' : 'لا ✅'; ?></p>
                    </div>
                    
                    <?php if ($fileInfo['has_bom']): ?>
                    <div class="section warning">
                        <h4>⚠️ تم العثور على BOM</h4>
                        <p>تم العثور على Byte Order Mark في بداية الملف. سيتم إزالته تلقائياً.</p>
                    </div>
                    <?php endif; ?>
                    
                    <h4>🔍 أسماء الأعمدة (أول 10 أعمدة):</h4>
                    <table>
                        <tr><th>الرقم</th><th>قبل التنظيف</th><th>بعد التنظيف</th></tr>
                        <?php
                        $originalHeaders = str_getcsv(explode("\n", file_get_contents($_FILES['csv_file']['tmp_name']))[0]);
                        $cleanHeaders = [];
                        foreach (array_slice($originalHeaders, 0, 10) as $index => $header) {
                            $cleanHeader = trim(str_replace("\xEF\xBB\xBF", '', $header));
                            $cleanHeaders[] = $cleanHeader;
                            
                            echo "<tr>";
                            echo "<td>" . ($index + 1) . "</td>";
                            echo "<td>" . htmlspecialchars($header) . "</td>";
                            echo "<td>" . htmlspecialchars($cleanHeader) . "</td>";
                            echo "</tr>";
                        }
                        ?>
                    </table>
                    <?php if (count($originalHeaders) > 10): ?>
                    <p><em>... و <?php echo count($originalHeaders) - 10; ?> عمود إضافي</em></p>
                    <?php endif; ?>
                </div>
                
                <?php if ($downloadLink): ?>
                <div class="section success">
                    <h4>✅ تم إصلاح الملف بنجاح!</h4>
                    <p><strong>الملف الجديد:</strong> <?php echo htmlspecialchars($fixedFileName); ?></p>
                    <p><strong>التغييرات المطبقة:</strong></p>
                    <ul>
                        <li>تم إزالة BOM (Byte Order Mark)</li>
                        <li>تم تنظيف أسماء الأعمدة</li>
                        <li>تم إزالة الأسطر الفارغة</li>
                        <li>تم تنسيق CSV بشكل صحيح</li>
                    </ul>
                    
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 100%;"></div>
                    </div>
                    
                    <a href="<?php echo $downloadLink; ?>" class="btn btn-success">📥 تحميل الملف المُصحح</a>
                </div>
                <?php else: ?>
                <div class="section error">
                    <h4>❌ خطأ في حفظ الملف</h4>
                    <p>لا يمكن حفظ الملف المُصحح. تحقق من صلاحيات المجلد المؤقت.</p>
                </div>
                <?php endif; ?>
                
                <div class="section">
                    <h4>🔄 معالجة ملف آخر</h4>
                    <a href="fix_csv_advanced.php" class="btn">إصلاح ملف جديد</a>
                </div>
                
            <?php else: ?>
                <!-- نموذج رفع الملف -->
                <div class="section">
                    <h3>📤 رفع ملف CSV للإصلاح</h3>
                    <p>ارفع ملف CSV لإزالة BOM وتنظيف أسماء الأعمدة:</p>
                    
                    <form method="post" enctype="multipart/form-data" id="uploadForm">
                        <div class="upload-area" id="uploadArea">
                            <p>📁 اختر ملف CSV أو اسحبه هنا</p>
                            <input type="file" name="csv_file" accept=".csv" required id="fileInput">
                            <p><small>الحد الأقصى: 50 ميجابايت</small></p>
                        </div>
                        <button type="submit" class="btn" id="submitBtn">🔧 إصلاح الملف</button>
                    </form>
                </div>
                
                <!-- معلومات مفيدة -->
                <div class="section warning">
                    <h3>💡 معلومات مفيدة</h3>
                    <h4>ما هو BOM؟</h4>
                    <p>BOM (Byte Order Mark) هو تسلسل من البايتات يُضاف في بداية ملفات النص لتحديد ترميز الملف. يمكن أن يسبب مشاكل في استيراد البيانات.</p>
                    
                    <h4>متى تحتاج لاستخدام هذه الأداة؟</h4>
                    <ul>
                        <li>عندما تظهر رسائل خطأ مثل "Unknown column '﻿رقم الطلب'"</li>
                        <li>عندما تجد رموز غريبة في بداية أسماء الأعمدة</li>
                        <li>عندما يفشل استيراد ملف CSV بدون سبب واضح</li>
                        <li>عند التعامل مع ملفات كبيرة الحجم</li>
                    </ul>
                    
                    <h4>المميزات الجديدة:</h4>
                    <ul>
                        <li>دعم الملفات الكبيرة (حتى 50 ميجابايت)</li>
                        <li>معالجة أسرع وأكثر كفاءة</li>
                        <li>واجهة محسنة مع شريط التقدم</li>
                        <li>تنظيف تلقائي للملفات المؤقتة</li>
                    </ul>
                </div>
            <?php endif; ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="import_data.php" class="btn">استيراد البيانات</a>
                <a href="debug_import.php" class="btn">تشخيص الاستيراد</a>
            </div>
        </div>
    </div>
    
    <script>
        // تحسين واجهة رفع الملفات
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const submitBtn = document.getElementById('submitBtn');
            
            // Drag and drop functionality
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#28a745';
                uploadArea.style.background = '#d4edda';
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#667eea';
                uploadArea.style.background = '';
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#667eea';
                uploadArea.style.background = '';
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    updateFileInfo(files[0]);
                }
            });
            
            fileInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    updateFileInfo(this.files[0]);
                }
            });
            
            function updateFileInfo(file) {
                const info = document.createElement('p');
                info.innerHTML = `<strong>الملف المحدد:</strong> ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} ميجابايت)`;
                uploadArea.appendChild(info);
                submitBtn.style.background = '#28a745';
            }
        });
    </script>
</body>
</html>
