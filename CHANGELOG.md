# سجل التغييرات - TIaF Report System

## الإصدار 1.4.7 - 2025-06-17

### 🚀 حل مشكلة الملفات الكبيرة (POST Content-Length)

#### 📁 مشكلة الملفات الكبيرة محلولة
- **حل مشكلة "POST Content-Length exceeds limit"** للملفات الكبيرة
- **دعم ملفات حتى 100 ميجابايت** مع معالجة متقدمة
- **استيراد بالدفعات** لتجنب مشاكل الذاكرة
- **شريط تقدم مباشر** لمتابعة عملية الاستيراد

#### 🛠️ أدوات جديدة للملفات الكبيرة:

##### 🚀 أداة استيراد الملفات الكبيرة (`large_file_import.php`):
- **زيادة حدود PHP** تلقائياً (512M ذاكرة، 300 ثانية تنفيذ)
- **معالجة بالدفعات** (500-5000 سجل لكل دفعة)
- **شريط تقدم مباشر** مع إحصائيات مفصلة
- **كشف مشاكل الحجم** مع حلول مقترحة
- **معالجة آمنة** مع transactions لضمان سلامة البيانات

##### ✂️ أداة تقسيم CSV (`split_csv.php`):
- **تقسيم الملفات الكبيرة** إلى ملفات أصغر
- **خيارات مرنة** للتقسيم (1000-25000 سطر لكل ملف)
- **الحفاظ على العناوين** في كل ملف مقسم
- **روابط تحميل مباشرة** للملفات المقسمة
- **تعليمات واضحة** لاستيراد الملفات المقسمة

##### 📥 أداة تحميل الملفات المقسمة (`download_split_file.php`):
- **تحميل آمن** للملفات المقسمة
- **حماية من الوصول غير المصرح** به
- **تحقق من صحة المسارات** والأنواع المسموحة

#### ⚙️ إعدادات Apache محسنة (`.htaccess`):
```apache
# زيادة حدود رفع الملفات
php_value upload_max_filesize 100M
php_value post_max_size 100M
php_value max_execution_time 300
php_value memory_limit 512M
php_value max_input_vars 3000
```

#### 🔧 التحسينات المطبقة:

##### معالجة الملفات الكبيرة:
- **استيراد بالدفعات**: معالجة 500-5000 سجل في كل مرة
- **إدارة الذاكرة**: تحرير الذاكرة بين الدفعات
- **معالجة الأخطاء**: rollback تلقائي في حالة الفشل
- **مراقبة التقدم**: تحديث مباشر لحالة الاستيراد

##### واجهة المستخدم المحسنة:
- **شريط تقدم مرئي**: عرض نسبة الإنجاز
- **إحصائيات مفصلة**: عدد السجلات المعالجة والأخطاء
- **رسائل واضحة**: تشخيص دقيق للمشاكل
- **حلول مقترحة**: إرشادات لحل المشاكل

#### 📊 أحجام الملفات المدعومة:

| حجم الملف | الطريقة الموصى بها | حجم الدفعة |
|-----------|-------------------|------------|
| < 10 ميجابايت | الاستيراد العادي | 2000-5000 |
| 10-50 ميجابايت | استيراد الملفات الكبيرة | 1000-2000 |
| 50-100 ميجابايت | استيراد الملفات الكبيرة | 500-1000 |
| > 100 ميجابايت | تقسيم CSV أولاً | 1000-5000 |

#### 🎯 حلول المشاكل الشائعة:

##### مشكلة "POST Content-Length exceeds limit":
```
✅ الحل: استخدم large_file_import.php
✅ البديل: قسم الملف باستخدام split_csv.php
```

##### مشكلة "Maximum execution time exceeded":
```
✅ الحل: تقليل حجم الدفعة إلى 500 سجل
✅ البديل: تقسيم الملف إلى ملفات أصغر
```

##### مشكلة "Memory limit exceeded":
```
✅ الحل: زيادة memory_limit في .htaccess
✅ البديل: استخدام معالجة الدفعات
```

#### 📁 الملفات الجديدة:
- `large_file_import.php` - استيراد الملفات الكبيرة بمعالجة متقدمة
- `split_csv.php` - تقسيم ملفات CSV الكبيرة
- `download_split_file.php` - تحميل الملفات المقسمة
- `.htaccess` - إعدادات Apache محسنة
- `temp/split_files/` - مجلد الملفات المقسمة المؤقتة

#### 📁 الملفات المحدثة:
- `index.php` - إضافة روابط الأدوات الجديدة
- `CHANGELOG.md` - توثيق الحلول الجديدة

#### ✅ النتائج المحققة:
- **حل نهائي** لمشكلة الملفات الكبيرة
- **دعم ملفات حتى 100 ميجابايت** بدون مشاكل
- **استيراد موثوق** مع معالجة الأخطاء
- **واجهة سهلة الاستخدام** مع إرشادات واضحة

#### 🚀 كيفية الاستخدام:

##### للملفات الكبيرة (10-100 ميجابايت):
```
http://localhost/TIaF-Report/large_file_import.php
← اختر الجدول وحجم الدفعة
← ارفع الملف
← راقب شريط التقدم
```

##### للملفات الكبيرة جداً (> 100 ميجابايت):
```
1. http://localhost/TIaF-Report/split_csv.php
   ← قسم الملف إلى أجزاء أصغر

2. http://localhost/TIaF-Report/large_file_import.php
   ← استورد كل جزء على حدة
```

---

## الإصدار 1.4.6 - 2025-06-17

### 🎯 توحيد تحويل أسماء الأعمدة مع debug_import.php

#### 📋 استخدام نفس دالة التحويل في جميع الملفات
- **توحيد دالة `getColumnMapping()`** في جميع أجزاء النظام
- **مطابقة تامة** مع التحويل الموجود في `debug_import.php`
- **إزالة التحويلات المكررة** والمتضاربة
- **ضمان الاتساق** في جميع عمليات الاستيراد

#### 🛠️ أداة جديدة للإنشاء بالتحويل الموحد:

##### 🎯 أداة إنشاء الجداول بالتحويل (`recreate_tables_with_mapping.php`):
- **استخدام نفس دالة التحويل** من `debug_import.php`
- **إنشاء الجداول الثلاثة** بأسماء الحقول الصحيحة
- **تحديد الحقول المناسبة** لكل جدول:
  - **جدول المحاسبة**: 23 حقل + export_date
  - **جدول إنهاء النزاع**: 35 حقل + export_date
  - **جدول تسوية النزاع**: 35 حقل + export_date
- **أنواع بيانات محسنة** لكل حقل
- **تعليقات باللغتين** (عربي وإنجليزي)

#### 📊 دالة التحويل الموحدة:
```php
function getColumnMapping() {
    return [
        'رقم الطلب' => 'request_number',
        'تاريخ الإنشاء' => 'creation_date',
        'منشئ الطلب' => 'created_by',
        'تاريخ التعديل' => 'modification_date',
        'معدل الطلب' => 'modified_by',
        'حالة الطلب' => 'request_status',
        'وصف الحالة' => 'status_description',
        'نوع الطلب' => 'request_type',
        'اسم نوع الطلب' => 'request_type_name',
        'رقم التسجيل الضريبي' => 'tax_registration_number',
        'كود المأمورية' => 'office_code',
        'اسم المأمورية' => 'office_name',
        'اسم المكلف' => 'taxpayer_name',
        'العنوان' => 'address',
        'رقم الهاتف' => 'phone_number',
        'البريد الإلكتروني' => 'email',
        'نوع المحاسبة' => 'accounting_type',
        'وصف نوع المحاسبة' => 'accounting_type_description',
        'الوعاء' => 'container',
        'اسم الوعاء' => 'container_name',
        'الفترة' => 'period',
        'مرحلة النزاع' => 'dispute_stage',
        'وصف مرحلة النزاع' => 'dispute_stage_description',
        'رقم القضية' => 'case_number',
        'جهة النزاع' => 'dispute_authority',
        'اسم جهة النزاع' => 'dispute_authority_name',
        'جهة أخرى' => 'other_authority',
        'الضريبة طبقاً للإقرار' => 'tax_according_to_declaration',
        'الضريبة من آخر ربط' => 'tax_from_last_assessment',
        'سنة آخر ربط' => 'last_assessment_year',
        'الضريبة طبقاً للنموذج' => 'tax_according_to_form',
        'الضريبة المسددة' => 'tax_paid',
        'الضريبة المتوقعة' => 'expected_tax',
        'الضريبة طبقاً للقانون' => 'tax_according_to_law',
        'الضريبة المستحقة' => 'tax_due_payment'
    ];
}
```

#### 🔧 التحسينات المطبقة:
- **إزالة التحويلات الإنجليزية** غير المستخدمة
- **تبسيط دالة التحويل** للتركيز على الأسماء العربية فقط
- **تحسين أنواع البيانات** لكل حقل
- **إضافة تعليقات واضحة** لكل حقل

#### 📁 هيكل الجداول المحدث:

##### جدول المحاسبة (23 حقل):
```
الحقول الأساسية (21 حقل):
- معلومات الطلب (9 حقول)
- معلومات المكلف (7 حقول)
- معلومات المحاسبة (3 حقول)
- الحقول المالية (2 حقل)
+ export_date
```

##### جداول النزاع (35 حقل لكل منهما):
```
جميع حقول المحاسبة (21 حقل) +
حقول النزاع الإضافية (14 حقل):
- معلومات النزاع (7 حقول)
- الحقول المالية الإضافية (7 حقول)
+ export_date
```

#### 📁 الملفات الجديدة:
- `recreate_tables_with_mapping.php` - إنشاء الجداول بالتحويل الموحد

#### 📁 الملفات المحدثة:
- `import_data.php` - تحديث دالة التحويل لتطابق debug_import.php
- `index.php` - إضافة رابط الأداة الجديدة
- `CHANGELOG.md` - توثيق التحديثات

#### ✅ الفوائد:
- **اتساق كامل** في تحويل أسماء الأعمدة
- **لا مزيد من التضارب** بين الملفات المختلفة
- **استيراد موثوق** مع ضمان التطابق
- **سهولة الصيانة** مع دالة تحويل موحدة

#### 🚀 كيفية الاستخدام:
1. **أنشئ الجداول**: `recreate_tables_with_mapping.php`
2. **استورد البيانات**: `import_data.php` (يستخدم نفس التحويل)
3. **تشخيص المشاكل**: `debug_import.php` (نفس التحويل)

---

## الإصدار 1.4.5 - 2025-06-17

### 🎯 مطابقة تامة لأسماء الأعمدة في ملفات CSV

#### 📋 إنشاء الجداول بأسماء الحقول المطابقة تماماً
- **أسماء الحقول مطابقة 100%** لأسماء الأعمدة في ملفات CSV
- **لا حاجة لتحويل أسماء الأعمدة** - المطابقة تلقائية
- **استيراد مباشر** بدون أخطاء في أسماء الحقول
- **دعم جميع الرموز والأحرف** في أسماء الأعمدة

#### 🛠️ أدوات جديدة للمطابقة التامة:

##### 📋 أداة استخراج أسماء الأعمدة (`extract_column_names.php`):
- **استخراج أسماء الأعمدة الفعلية** من ملفات CSV
- **إنشاء أسماء حقول مطابقة** تلقائياً
- **عرض تفصيلي** لكل عمود ونوع البيانات المقترح
- **إنشاء SQL جاهز** للتنفيذ المباشر
- **دالة تحويل PHP** جاهزة للاستخدام

##### 🔄 أداة إنشاء الجداول من CSV (`recreate_tables_from_csv.php`):
- **إنشاء الجداول مباشرة** من ملفات CSV
- **مطابقة تامة** لأسماء الأعمدة
- **حذف وإعادة إنشاء** الجداول تلقائياً
- **عرض تفصيلي** لعملية الإنشاء
- **إنشاء دالة التحويل** تلقائياً

##### 📥 أداة الاستيراد المطابق (`import_data_exact_match.php`):
- **استيراد مباشر** بدون تحويل أسماء الأعمدة
- **مطابقة تلقائية** لأسماء الحقول
- **التحقق من وجود الحقول** قبل الاستيراد
- **رسائل خطأ واضحة** في حالة عدم التطابق
- **دعم كامل** لجميع أنواع البيانات

#### 🔧 آلية المطابقة التامة:

##### خطوات العمل:
```
1. استخراج أسماء الأعمدة من CSV
2. تحويل الأسماء إلى أسماء حقول صالحة
3. إنشاء الجداول بالأسماء المطابقة
4. استيراد البيانات مباشرة بدون تحويل
```

##### مثال على التحويل:
```
اسم العمود في CSV: "رقم الطلب"
اسم الحقل في قاعدة البيانات: "رقم_الطلب"
التعليق: "رقم الطلب"
```

#### 📊 المميزات الجديدة:
- **مطابقة 100%** لأسماء الأعمدة
- **دعم الأحرف العربية** والرموز الخاصة
- **تحويل تلقائي** للمسافات والرموز
- **حفظ الاسم الأصلي** في COMMENT
- **استيراد مباشر** بدون أخطاء

#### 🎯 حل نهائي لمشاكل الاستيراد:
- **لا مزيد من أخطاء "Unknown column"**
- **لا حاجة لتعديل ملفات CSV**
- **لا حاجة لتحويل أسماء الأعمدة يدوياً**
- **استيراد ناجح 100%** في المرة الأولى

#### 📁 الملفات الجديدة:
- `extract_column_names.php` - استخراج أسماء الأعمدة من CSV
- `recreate_tables_from_csv.php` - إنشاء الجداول من CSV مباشرة
- `import_data_exact_match.php` - استيراد مع مطابقة تامة

#### 📁 الملفات المحدثة:
- `index.php` - إضافة روابط الأدوات الجديدة
- `CHANGELOG.md` - توثيق التحديثات

#### ✅ النتيجة النهائية:
```
بدلاً من:
❌ تم بنجاح: 0
❌ أخطاء: 3267
❌ Unknown column '﻿رقم الطلب'

ستحصل على:
✅ تم بنجاح: 3267
✅ أخطاء: 0
✅ استيراد مثالي بدون أي مشاكل
```

#### 🚀 كيفية الاستخدام:
1. **استخرج أسماء الأعمدة**: `extract_column_names.php`
2. **أنشئ الجداول من CSV**: `recreate_tables_from_csv.php`
3. **استورد البيانات**: `import_data_exact_match.php`

---

## الإصدار 1.4.4 - 2025-06-17

### 🔄 إعادة إنشاء الجداول بالعدد الصحيح للحقول

#### 📊 تصحيح هيكل الجداول الأساسية
- **جدول المحاسبة (accounting):** 23 حقل + export_date
- **جدول إنهاء النزاع (conflict):** 39 حقل + export_date
- **جدول تسوية النزاع (dispute):** 39 حقل + export_date

#### 🛠️ أدوات جديدة لإدارة الجداول:

##### 🔍 أداة تحليل هيكل CSV (`analyze_csv_structure.php`):
- **تحليل ملفات CSV** لتحديد عدد الحقول الصحيح
- **اقتراح أسماء الحقول الإنجليزية** تلقائياً
- **تحديد أنواع البيانات المناسبة**
- **إنشاء SQL جاهز** لإنشاء الجداول
- **دعم رفع ملفات متعددة** للتحليل

##### 🔄 أداة إعادة إنشاء الجداول (`recreate_main_tables_correct.php`):
- **حذف الجداول الموجودة** وإعادة إنشائها
- **إنشاء الجداول بالعدد الصحيح للحقول**
- **أسماء الحقول باللغتين** (عربي وإنجليزي)
- **فهارس محسنة** للبحث والاستعلام
- **تقرير مفصل** عن عملية الإنشاء

#### 📋 هيكل الجداول المحدث:

##### جدول المحاسبة (23 حقل):
```sql
- request_number (رقم الطلب)
- creation_date (تاريخ الإنشاء)
- created_by (منشئ الطلب)
- modification_date (تاريخ التعديل)
- modified_by (معدل الطلب)
- request_status (حالة الطلب)
- status_description (وصف الحالة)
- request_type (نوع الطلب)
- request_type_name (اسم نوع الطلب)
- tax_registration_number (رقم التسجيل الضريبي)
- office_code (كود المأمورية)
- office_name (اسم المأمورية)
- taxpayer_name (اسم المكلف)
- address (العنوان)
- phone_number (رقم الهاتف)
- email (البريد الإلكتروني)
- accounting_type (نوع المحاسبة)
- accounting_type_description (وصف نوع المحاسبة)
- container (الوعاء)
- container_name (اسم الوعاء)
- period (الفترة)
- expected_tax (الضريبة المتوقعة)
- tax_according_to_law (الضريبة طبقاً للقانون)
+ export_date (تاريخ التصدير)
```

##### جداول إنهاء النزاع وتسوية النزاع (39 حقل):
```sql
جميع حقول المحاسبة + 16 حقل إضافي:
- dispute_stage (مرحلة النزاع)
- dispute_stage_description (وصف مرحلة النزاع)
- case_number (رقم القضية)
- dispute_authority (جهة النزاع)
- dispute_authority_name (اسم جهة النزاع)
- other_authority (جهة أخرى)
- tax_according_to_declaration (الضريبة طبقاً للإقرار)
- tax_from_last_assessment (الضريبة من آخر ربط)
- last_assessment_year (سنة آخر ربط)
- tax_according_to_form (الضريبة طبقاً للنموذج)
- tax_paid (الضريبة المسددة)
- tax_according_to_declaration_before_modification
- tax_from_last_assessment_before_modification
- tax_according_to_form_before_modification
- tax_paid_before_modification
- tax_due_payment (الضريبة المستحقة)
```

#### 🔧 التحسينات التقنية:
- **أسماء الحقول باللغتين** في COMMENT
- **فهارس محسنة** للبحث السريع
- **بدون مفتاح أساسي id** كما هو مطلوب
- **تنظيم البيانات حسب export_date**

#### 📁 الملفات الجديدة:
- `analyze_csv_structure.php` - تحليل هيكل ملفات CSV
- `recreate_main_tables_correct.php` - إعادة إنشاء الجداول بالهيكل الصحيح

#### 📁 الملفات المحدثة:
- `index.php` - إضافة روابط الأدوات الجديدة
- `CHANGELOG.md` - توثيق التحديثات

#### ✅ الفوائد:
- **مطابقة تامة** لملفات CSV الفعلية
- **استيراد ناجح** بدون أخطاء في أسماء الأعمدة
- **أداء محسن** مع الفهارس المناسبة
- **سهولة الصيانة** مع أسماء واضحة باللغتين

#### 🎯 كيفية الاستخدام:
1. **تحليل الملفات**: استخدم `analyze_csv_structure.php` لفحص ملفات CSV
2. **إعادة إنشاء الجداول**: استخدم `recreate_main_tables_correct.php`
3. **استيراد البيانات**: استخدم `import_data.php` مع الجداول الجديدة

---

## الإصدار 1.4.3 - 2025-06-17

### 🔧 إصلاح مشكلة "Request-URI Too Long"

#### 📁 حل مشكلة الملفات الكبيرة
- **إصلاح خطأ "Request-URI Too Long"** عند تحميل ملفات CSV كبيرة
- **استخدام ملفات مؤقتة** بدلاً من base64 encoding
- **تحسين معالجة الذاكرة** للملفات الكبيرة
- **دعم ملفات حتى 50 ميجابايت**

#### 🛠️ أدوات جديدة ومحسنة:

##### 🔧 أداة إصلاح CSV المتقدمة (`fix_csv_advanced.php`):
- **معالجة الملفات الكبيرة** بكفاءة عالية
- **واجهة محسنة** مع drag & drop
- **شريط تقدم** وتحديثات مرئية
- **تنظيف تلقائي** للملفات المؤقتة
- **دعم أفضل للملفات متعددة الأعمدة**

##### 🧹 أداة تنظيف الملفات المؤقتة (`cleanup_temp.php`):
- **تنظيف تلقائي** للملفات القديمة
- **مراقبة استخدام المساحة**
- **إحصائيات مفصلة** عن الملفات المؤقتة
- **دعم cron jobs** للتنظيف المجدول

#### 🔄 التحسينات التقنية:
- **استخدام POST** بدلاً من GET للتحميل
- **حفظ مؤقت آمن** في مجلد النظام
- **تنظيف تلقائي** بعد التحميل
- **معالجة أفضل للأخطاء**

#### 📁 الملفات الجديدة:
- `fix_csv_advanced.php` - أداة إصلاح متقدمة للملفات الكبيرة
- `cleanup_temp.php` - أداة تنظيف الملفات المؤقتة

#### 📁 الملفات المحدثة:
- `fix_csv_simple.php` - إصلاح مشكلة session headers
- `index.php` - إضافة خيارات متعددة لإصلاح CSV
- `CHANGELOG.md` - توثيق الإصلاحات

#### ✅ الفوائد:
- **دعم الملفات الكبيرة** (حتى 50 ميجابايت)
- **أداء أفضل** وذاكرة أقل
- **واجهة أكثر احترافية**
- **تنظيف تلقائي** للملفات المؤقتة
- **استقرار أكبر** في المعالجة

#### 🎯 كيفية الاستخدام:
1. **للملفات الكبيرة**: استخدم `fix_csv_advanced.php`
2. **للملفات الصغيرة**: استخدم `fix_csv_simple.php`
3. **للتنظيف**: استخدم `cleanup_temp.php`

---

## الإصدار 1.4.2 - 2025-06-17

### 🔧 إصلاح مشاكل استيراد CSV

#### 📁 حل مشكلة أسماء الأعمدة
- **إصلاح مشكلة BOM** (Byte Order Mark) في ملفات CSV
- **تحويل أسماء الأعمدة العربية** إلى أسماء الحقول الإنجليزية
- **تنظيف أسماء الأعمدة** وإزالة المسافات والرموز الخفية
- **معالجة الأخطاء** مثل "Unknown column '﻿رقم الطلب'"

#### 🛠️ أدوات جديدة للتشخيص والإصلاح:

##### 🔍 أداة تشخيص الاستيراد (`debug_import.php`):
- **عرض هيكل الجداول** في قاعدة البيانات
- **تحليل ملفات CSV** وأسماء الأعمدة
- **جدول تحويل الأسماء** العربية إلى الإنجليزية
- **التحقق من التوافق** بين CSV وقاعدة البيانات
- **عرض عينة من البيانات** للفحص

##### 🔧 أداة إصلاح CSV (`fix_csv.php`):
- **إزالة BOM** من ملفات CSV
- **تنظيف أسماء الأعمدة** تلقائياً
- **إزالة الأسطر الفارغة**
- **تحميل الملف المُصحح** مباشرة

#### 📋 تحويل أسماء الأعمدة:
```php
'رقم الطلب' => 'request_number'
'اسم المكلف' => 'taxpayer_name'
'كود المأمورية' => 'office_code'
'اسم المأمورية' => 'office_name'
'حالة الطلب' => 'request_status'
// ... والمزيد
```

#### 🔄 تحسينات الاستيراد:
- **معالجة BOM** تلقائياً أثناء الاستيراد
- **تحويل أسماء الأعمدة** تلقائياً
- **رسائل خطأ أوضح** مع تفاصيل المشكلة
- **عرض تقدم العملية** مع إحصائيات مفصلة

#### 📁 الملفات الجديدة:
- `debug_import.php` - أداة تشخيص مشاكل الاستيراد
- `fix_csv.php` - أداة إصلاح ملفات CSV
- `download_fixed.php` - تحميل الملفات المُصححة

#### 📁 الملفات المحدثة:
- `import_data.php` - إضافة معالجة BOM وتحويل أسماء الأعمدة
- `index.php` - إضافة روابط الأدوات الجديدة
- `CHANGELOG.md` - توثيق الإصلاحات

#### ✅ الفوائد:
- **حل نهائي** لمشاكل استيراد CSV
- **تشخيص سريع** للمشاكل
- **إصلاح تلقائي** للملفات
- **سهولة الاستخدام** مع واجهات بديهية

#### 🎯 كيفية الاستخدام:
1. **إذا فشل الاستيراد**: استخدم `debug_import.php` لتشخيص المشكلة
2. **إذا كانت مشكلة BOM**: استخدم `fix_csv.php` لإصلاح الملف
3. **ثم قم بالاستيراد** باستخدام الملف المُصحح

---

## الإصدار 1.4.1 - 2025-06-17

### 🔧 إصلاحات مهمة

#### 📑 إصلاح نظام تصدير PDF
- **إصلاح مسارات الملفات** في `includes/pdf_export.php`
- **تحسين نظام الطباعة** بدلاً من PDF معقد
- **إضافة CSS للطباعة** مع تنسيق محسن
- **دعم الطباعة التلقائية** عند فتح التقرير

#### 🧪 إضافة نظام اختبار شامل
- **ملف اختبار جديد**: `test_system.php`
- **فحص الاتصال بقاعدة البيانات**
- **التحقق من وجود الجداول**
- **اختبار الدوال المهمة**
- **فحص الملفات الأساسية**
- **تقرير مفصل** عن حالة النظام

#### 🛠️ التحسينات التقنية:
- تصحيح مسارات `require_once` في ملفات PDF
- تحسين دالة `exportToPrintableHTML()`
- إضافة CSS محسن للطباعة
- تحسين معالجة الأخطاء

#### 📁 الملفات المحدثة:
- `includes/pdf_export.php` - إصلاح المسارات وتحسين الطباعة
- `index.php` - إضافة رابط اختبار النظام
- `test_system.php` - ملف اختبار شامل جديد
- `CHANGELOG.md` - توثيق الإصلاحات

#### ✅ الفوائد:
- **حل مشكلة تصدير PDF** نهائياً
- **نظام اختبار شامل** لتشخيص المشاكل
- **طباعة محسنة** للتقارير
- **سهولة الصيانة** والتشخيص

---

## الإصدار 1.4 - 2025-06-17

### 🔧 تصحيح هيكل الجداول الأساسية

#### ⚠️ تغيير مهم في هيكل قاعدة البيانات
- **إزالة المفتاح الأساسي id** من الجداول الثلاثة الأساسية
- **تنظيم البيانات حسب export_date** المستخرج من اسم الملف
- **تنسيق أسماء الملفات الصحيح**: `TableNameEXPORT_YYYYMMDD.csv`

#### 📁 أمثلة أسماء الملفات:
- `AccountingEXPORT_20250526.csv`
- `ConflictEXPORT_20250526.csv`
- `DisputeEXPORT_20250526.csv`

#### 🗄️ الهيكل الجديد للجداول:
```sql
-- بدون مفتاح أساسي id
CREATE TABLE `accounting` (
    `request_number` varchar(50),
    `taxpayer_name` varchar(255),
    `office_code` varchar(10),
    `office_name` varchar(255),
    `export_date` date NOT NULL, -- المرجع الأساسي
    -- باقي الحقول...
    KEY `idx_export_date` (`export_date`),
    KEY `idx_request_number` (`request_number`)
);
```

#### 🛠️ الملفات الجديدة:
- `recreate_main_tables.php` - إعادة إنشاء الجداول الأساسية بالهيكل الصحيح

#### 🔄 الملفات المحدثة:
- `includes/functions.php` - إضافة دوال استخراج التاريخ ونوع الجدول
- `index.php` - إضافة رابط إعادة إنشاء الجداول الأساسية
- `README.md` - تحديث التوثيق
- `CHANGELOG.md` - توثيق التغييرات

#### ✅ الفوائد:
- **مطابقة تامة** لمتطلبات النظام
- **تنظيم أفضل** للبيانات حسب تاريخ التصدير
- **استيراد أسرع** بدون قيود المفتاح الأساسي
- **مرونة أكبر** في إدارة البيانات

---

## الإصدار 1.3 - 2025-06-17

### 🎉 الإصدار الكامل مع جميع المميزات المطلوبة

#### 📊 لوحة التحكم التفاعلية
- **مخططات بيانية تفاعلية** باستخدام Chart.js
- **مؤشرات الأداء الرئيسية (KPIs)** في الوقت الفعلي
- **إحصائيات شاملة** لجميع أنواع الطلبات
- **تحليل البيانات** حسب المناطق ومديري العموم
- **واجهة سهلة الاستخدام** مع تأثيرات بصرية جذابة

#### 📈 التقارير المتخصصة
- **تقارير منفصلة** لكل نوع من أنواع الطلبات:
  - تقارير طلبات المحاسبة
  - تقارير طلبات إنهاء النزاع
  - تقارير طلبات تسوية النزاع
- **تصفية متقدمة** حسب:
  - التاريخ (من وإلى)
  - مديري العموم
  - الاختصاص
  - المنطقة
  - المامورية
  - حالة الطلب
  - نوع الوعاء
- **ترقيم الصفحات** للتعامل مع البيانات الكبيرة
- **إحصائيات فورية** لكل تقرير

#### 🔍 نظام البحث المتقدم
- **بحث شامل** في جميع الجداول
- **بحث نصي** في رقم الطلب، اسم المكلف، المأمورية، البريد الإلكتروني، رقم الهاتف
- **فلاتر متقدمة** مع جميع معايير التصفية
- **نتائج مرتبة** حسب التاريخ والأهمية
- **واجهة بحث سهلة** مع معاينة فورية للنتائج

#### 🌙 وضع الليل/النهار
- **تبديل تلقائي** بين الوضع النهاري والليلي
- **حفظ التفضيلات** في التخزين المحلي
- **تصميم متجاوب** مع جميع عناصر الواجهة
- **اختصار لوحة المفاتيح** (Ctrl + Shift + T)
- **تكامل مع تفضيلات النظام**

#### 📑 تصدير PDF متقدم
- **تصدير التقارير إلى PDF** مع تنسيق احترافي
- **تضمين الفلاتر المطبقة** في التقرير
- **إحصائيات مفصلة** في كل تقرير
- **تصميم عربي** مع دعم كامل للنصوص العربية
- **خيارات طباعة** محسنة

### 🛠️ التحسينات التقنية

#### الملفات الجديدة:
- `dashboard.php` - لوحة التحكم الرئيسية
- `assets/js/dashboard.js` - JavaScript للمخططات البيانية
- `search.php` - نظام البحث المتقدم
- `assets/js/theme.js` - إدارة الثيمات
- `includes/pdf_export.php` - نظام تصدير PDF
- `reports/accounting.php` - تقارير المحاسبة
- `reports/conflict.php` - تقارير إنهاء النزاع
- `reports/dispute.php` - تقارير تسوية النزاع

#### الملفات المحدثة:
- `assets/css/main.css` - إضافة أنماط الوضع الليلي
- `index.php` - إضافة روابط المميزات الجديدة
- `includes/functions.php` - دوال محسنة للتقارير

### 🎯 المميزات المكتملة

✅ **لوحة التحكم مع المخططات البيانية**
✅ **صفحات التقارير المنفصلة** مع تصفية شاملة
✅ **نظام البحث المتقدم**
✅ **وضع الليل/النهار**
✅ **تصدير PDF** للتقارير

### 📊 الإحصائيات

- **5 صفحات جديدة** للتقارير والتحليل
- **4 ملفات JavaScript** للتفاعل والمخططات
- **3 أنواع تصدير** (CSV, PDF, طباعة)
- **10+ مخططات بيانية** تفاعلية
- **دعم كامل للغة العربية** في جميع المكونات

---

## الإصدار 1.2 - 2025-06-17

### ✅ إعادة هيكلة الجداول المساعدة

#### 🗄️ تحديث الجداول المرجعية
- **إعادة إنشاء جدول التصنيف**: 7 حقول طبقاً لملف "جدول التصنيف.csv"
  - التصنيف، كود المأمورية، اسم المأمورية، المنطقة، المامورية، الاختصاص، مديري العموم
- **إعادة إنشاء جدول الوعاء**: 3 حقول طبقاً لملف "جدول الوعاء.csv"
  - الوعاء، اسم الوعاء، التصنيف
- **إعادة إنشاء جدول حالة الطلب**: 2 حقل طبقاً لملف "جدول حالة الطلب.csv"
  - حالة الطلب، وصف الحالة

#### 📥 استيراد تلقائي من ملفات CSV
- **قراءة مباشرة** من ملفات Mapping Table
- **تنظيف البيانات** تلقائياً أثناء الاستيراد
- **التحقق من صحة البيانات** قبل الإدراج

#### 🛠️ أدوات إدارة جديدة
- **ملف إعادة الإنشاء**: `recreate_mapping_tables.php`
- **ملف عرض البيانات**: `view_mapping_tables.php`
- **تحديث الدوال المساعدة** في `includes/functions.php`

### 📁 الملفات الجديدة
- `recreate_mapping_tables.php` - إعادة إنشاء الجداول المساعدة
- `view_mapping_tables.php` - عرض محتويات الجداول المساعدة

### 📁 الملفات المحدثة
- `database_setup.php` - تحديث إنشاء الجداول المرجعية
- `includes/functions.php` - إضافة دوال للتعامل مع الجداول الجديدة
- `index.php` - إضافة روابط الأدوات الجديدة
- `README.md` - تحديث التوثيق
- `CHANGELOG.md` - توثيق التغييرات

---

## الإصدار 1.1 - 2025-06-17

### ✅ التحسينات الرئيسية

#### 🗄️ تحسين هيكل قاعدة البيانات
- **إزالة جميع القيود والمفاتيح المنفردة** من الجداول الأساسية (accounting, conflict, dispute)
- **تسهيل الاستيراد المستمر** للبيانات دون تعارضات
- **تحسين الأداء** عند استيراد كميات كبيرة من البيانات

#### 📥 تحسين نظام استيراد البيانات
- **خيار حذف البيانات السابقة**: يمكن اختيار حذف جميع البيانات أو بيانات نفس التاريخ فقط
- **حذف تلقائي للبيانات المكررة**: تجنب تكرار البيانات لنفس تاريخ التصدير
- **تحسين معالجة الأخطاء**: رسائل أوضح وتتبع أفضل للعملية

#### 🗂️ إضافة صفحة إدارة البيانات الجديدة
- **عرض إحصائيات مفصلة** لكل جدول (عدد السجلات، الحجم، التوزيع حسب التاريخ)
- **حذف جدول كامل** مع تأكيد الأمان
- **حذف بيانات تاريخ محدد** لتنظيف البيانات القديمة
- **تحسين الجداول** لتحسين الأداء

### 🔧 التغييرات التقنية

#### قاعدة البيانات:
```sql
-- قبل التحديث
CREATE TABLE accounting (
    id bigint PRIMARY KEY,
    request_number varchar(50) NOT NULL,
    export_date date NOT NULL,
    -- مفاتيح وفهارس متعددة
);

-- بعد التحديث
CREATE TABLE accounting (
    id bigint PRIMARY KEY,
    request_number varchar(50),
    export_date date,
    -- بدون قيود إضافية
);
```

#### استيراد البيانات:
```php
// إضافة خيار حذف البيانات
if ($clearTable) {
    $pdo->exec("DELETE FROM `$table`");
} else {
    $pdo->exec("DELETE FROM `$table` WHERE export_date = ?");
}
```

### 📋 الملفات المحدثة

#### ملفات جديدة:
- `data_management.php` - صفحة إدارة البيانات
- `CHANGELOG.md` - سجل التغييرات

#### ملفات محدثة:
- `database_setup_part2.php` - إزالة القيود من جداول accounting و conflict
- `database_setup_part3.php` - إزالة القيود من جدول dispute وإزالة الفهارس الإضافية
- `import_data.php` - إضافة خيارات الحذف وتحسين المعالجة
- `index.php` - إضافة رابط إدارة البيانات
- `README.md` - تحديث التوثيق

### 🎯 الفوائد

#### للمطورين:
- **سهولة الصيانة**: لا توجد قيود معقدة تعيق التطوير
- **مرونة في التحديث**: يمكن تعديل البيانات دون قيود
- **أداء أفضل**: استيراد أسرع للبيانات الكبيرة

#### للمستخدمين:
- **استيراد أسرع**: لا توجد تأخيرات بسبب فحص القيود
- **مرونة في الإدارة**: خيارات متعددة لحذف وتنظيف البيانات
- **شفافية أكبر**: إحصائيات مفصلة عن البيانات

### ⚠️ ملاحظات مهمة

#### قبل التحديث:
1. **عمل نسخة احتياطية** من قاعدة البيانات الحالية
2. **اختبار النظام** في بيئة تطوير أولاً
3. **التأكد من صحة البيانات** بعد التحديث

#### بعد التحديث:
1. **إعادة إنشاء قاعدة البيانات** باستخدام الملفات المحدثة
2. **إعادة استيراد البيانات** باستخدام النظام الجديد
3. **اختبار جميع الوظائف** للتأكد من عملها

### 🚀 الخطوات التالية

#### الإصدار القادم (1.2):
- [ ] إضافة لوحة التحكم مع المخططات البيانية
- [ ] تطوير صفحات التقارير المنفصلة
- [ ] إضافة نظام البحث المتقدم
- [ ] تطوير وضع الليل/النهار

#### تحسينات مستقبلية:
- [ ] إضافة نظام المستخدمين والصلاحيات
- [ ] تطوير API للتكامل مع الأنظمة الأخرى
- [ ] إضافة التصدير إلى PDF
- [ ] تحسين الأمان والحماية

---

## الإصدار 1.0 - 2025-06-17

### 🎉 الإطلاق الأولي
- إنشاء النظام الأساسي
- إعداد قاعدة البيانات
- تطوير نظام استيراد البيانات
- تصميم الواجهة العربية
- إنشاء الهيكل التنظيمي للمشروع

---

**ملاحظة**: هذا السجل يوثق جميع التغييرات المهمة في المشروع. للحصول على تفاصيل أكثر، راجع ملفات الكود المصدري.
