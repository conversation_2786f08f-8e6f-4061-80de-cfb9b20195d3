<?php
/**
 * استيراد البيانات مع مطابقة تامة لأسماء الأعمدة - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.4.4
 * @date 2025-06-17
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

// دالة لإنشاء اسم الحقل من اسم العمود (نفس الدالة المستخدمة في إنشاء الجداول)
function createFieldName($columnName) {
    // إزالة الرموز الخاصة والمسافات
    $fieldName = $columnName;
    $fieldName = str_replace([' ', '-', '/', '\\', '(', ')', '[', ']', '.', ',', ':', ';'], '_', $fieldName);
    $fieldName = preg_replace('/[^\p{L}\p{N}_]/u', '_', $fieldName);
    $fieldName = preg_replace('/_+/', '_', $fieldName);
    $fieldName = trim($fieldName, '_');
    $fieldName = strtolower($fieldName);
    
    // إذا كان الاسم فارغ أو يبدأ برقم، أضف بادئة
    if (empty($fieldName) || is_numeric($fieldName[0])) {
        $fieldName = 'field_' . $fieldName;
    }
    
    return $fieldName;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استيراد البيانات - مطابقة تامة - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .table-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            cursor: pointer;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .file-input {
            width: 100%;
            padding: 10px;
            border: 2px dashed #667eea;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📥 استيراد البيانات - مطابقة تامة</h1>
            <p>استيراد البيانات مع مطابقة تامة لأسماء الأعمدة في ملفات CSV</p>
        </div>
        
        <div class="content">
            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
                $table = $_POST['import_table'];
                $clearTable = isset($_POST['clear_table']);
                $file = $_FILES['csv_file'];
                
                if ($file['error'] === UPLOAD_ERR_OK) {
                    try {
                        $pdo = getDBConnection();
                        
                        echo "<div class='section'>";
                        echo "<h3>🔄 بدء عملية الاستيراد</h3>";
                        echo "<p><strong>الجدول:</strong> $table</p>";
                        echo "<p><strong>الملف:</strong> " . htmlspecialchars($file['name']) . "</p>";
                        echo "<p><strong>الحجم:</strong> " . number_format($file['size']) . " بايت</p>";
                        
                        // استخراج تاريخ التصدير من اسم الملف
                        $exportDate = extractExportDate($file['name']);
                        echo "<p><strong>تاريخ التصدير:</strong> $exportDate</p>";
                        
                        // حذف البيانات السابقة إذا طُلب ذلك
                        if ($clearTable) {
                            $pdo->exec("DELETE FROM `$table`");
                            echo "<p>🗑️ تم حذف جميع البيانات السابقة من الجدول</p>";
                        } else {
                            // حذف البيانات لنفس تاريخ التصدير
                            $stmt = $pdo->prepare("DELETE FROM `$table` WHERE export_date = ?");
                            $stmt->execute([$exportDate]);
                            $deletedRows = $stmt->rowCount();
                            echo "<p>🗑️ تم حذف $deletedRows سجل لتاريخ التصدير $exportDate</p>";
                        }
                        
                        // قراءة ملف CSV
                        $handle = fopen($file['tmp_name'], 'r');
                        
                        // قراءة العنوان الأول (أسماء الأعمدة)
                        $headers = fgetcsv($handle);
                        
                        // تنظيف أسماء الأعمدة وإزالة BOM
                        $cleanHeaders = array_map(function($header) {
                            // إزالة BOM (Byte Order Mark)
                            $header = str_replace("\xEF\xBB\xBF", '', $header);
                            // إزالة المسافات والرموز الخفية
                            $header = trim($header);
                            return $header;
                        }, $headers);
                        
                        // تحويل أسماء الأعمدة إلى أسماء الحقول
                        $fieldNames = array_map('createFieldName', $cleanHeaders);
                        
                        echo "<p>📋 تم العثور على " . count($fieldNames) . " عمود في الملف</p>";
                        echo "<p>🔄 أسماء الحقول: " . implode(', ', array_slice($fieldNames, 0, 5)) . "...</p>";
                        
                        // التحقق من وجود الحقول في الجدول
                        $stmt = $pdo->query("DESCRIBE `$table`");
                        $tableColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        
                        $missingFields = [];
                        foreach ($fieldNames as $field) {
                            if (!in_array($field, $tableColumns)) {
                                $missingFields[] = $field;
                            }
                        }
                        
                        if (!empty($missingFields)) {
                            echo "<div class='section error'>";
                            echo "<h4>❌ حقول غير موجودة في الجدول</h4>";
                            echo "<p>الحقول التالية غير موجودة في جدول $table:</p>";
                            echo "<ul>";
                            foreach ($missingFields as $field) {
                                echo "<li>$field</li>";
                            }
                            echo "</ul>";
                            echo "<p>يرجى إعادة إنشاء الجدول باستخدام أداة 'إنشاء من CSV'</p>";
                            echo "</div>";
                            fclose($handle);
                            return;
                        }
                        
                        // إعداد الاستعلام
                        $columns = implode('`, `', $fieldNames);
                        $placeholders = str_repeat('?,', count($fieldNames)) . '?'; // إضافة placeholder لتاريخ التصدير
                        $placeholders = rtrim($placeholders, ',');
                        
                        $sql = "INSERT INTO `$table` (`$columns`, `export_date`) VALUES ($placeholders)";
                        $stmt = $pdo->prepare($sql);
                        
                        echo "<p>🔧 استعلام SQL: " . substr($sql, 0, 100) . "...</p>";
                        
                        // استيراد البيانات
                        $successCount = 0;
                        $errorCount = 0;
                        $lineNumber = 1;
                        $errors = [];
                        
                        while (($data = fgetcsv($handle)) !== FALSE) {
                            $lineNumber++;
                            
                            if (count($data) !== count($fieldNames)) {
                                $errorCount++;
                                $errors[] = "السطر $lineNumber: عدد الأعمدة غير متطابق (" . count($data) . " بدلاً من " . count($fieldNames) . ")";
                                continue;
                            }
                            
                            try {
                                // إضافة تاريخ التصدير إلى البيانات
                                $data[] = $exportDate;
                                $stmt->execute($data);
                                $successCount++;
                            } catch (PDOException $e) {
                                $errorCount++;
                                $errors[] = "خطأ في السطر $lineNumber: " . $e->getMessage();
                            }
                        }
                        
                        fclose($handle);
                        
                        // عرض النتائج
                        echo "</div>";
                        
                        if ($successCount > 0) {
                            echo "<div class='section success'>";
                            echo "<h3>✅ تم الانتهاء من الاستيراد!</h3>";
                            echo "<p><strong>إجمالي السجلات:</strong> " . ($successCount + $errorCount) . "</p>";
                            echo "<p><strong>تم بنجاح:</strong> $successCount</p>";
                            echo "<p><strong>أخطاء:</strong> $errorCount</p>";
                            echo "</div>";
                        }
                        
                        if ($errorCount > 0 && !empty($errors)) {
                            echo "<div class='section error'>";
                            echo "<h3>❌ رسائل الخطأ</h3>";
                            echo "<p>نموذج من رسائل الخطأ:</p>";
                            foreach (array_slice($errors, 0, 5) as $error) {
                                echo "<p>$error</p>";
                            }
                            if (count($errors) > 5) {
                                echo "<p>... و " . (count($errors) - 5) . " خطأ إضافي</p>";
                            }
                            echo "</div>";
                        }
                        
                    } catch (Exception $e) {
                        echo "<div class='section error'>";
                        echo "<h3>❌ خطأ في العملية</h3>";
                        echo "<p>" . $e->getMessage() . "</p>";
                        echo "</div>";
                    }
                } else {
                    echo "<div class='section error'>";
                    echo "<h3>❌ خطأ في رفع الملف</h3>";
                    echo "<p>كود الخطأ: " . $file['error'] . "</p>";
                    echo "</div>";
                }
            }
            ?>
            
            <!-- معلومات مهمة -->
            <div class="section warning">
                <h3>⚠️ متطلبات مهمة</h3>
                <p>لضمان نجاح الاستيراد:</p>
                <ol>
                    <li><strong>يجب إنشاء الجداول أولاً</strong> باستخدام أداة "إنشاء من CSV"</li>
                    <li><strong>أسماء الحقول ستطابق تماماً</strong> أسماء الأعمدة في ملف CSV</li>
                    <li><strong>لا حاجة لتحويل أسماء الأعمدة</strong> - المطابقة تلقائية</li>
                    <li><strong>تنسيق اسم الملف:</strong> TableNameEXPORT_YYYYMMDD.csv</li>
                </ol>
            </div>
            
            <!-- نماذج الاستيراد -->
            <div class="table-info">
                <div class="table-card">
                    <h4>📊 طلبات المحاسبة (Accounting)</h4>
                    <p>استيراد بيانات طلبات المحاسبة من ملفات CSV بتنسيق AccountingEXPORT_YYYYMMDD.csv</p>
                    <form method="post" enctype="multipart/form-data" style="margin-top: 15px;">
                        <input type="hidden" name="import_table" value="accounting">
                        <input type="file" name="csv_file" accept=".csv" class="file-input" required>
                        <div style="margin: 10px 0;">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="clear_table" value="1">
                                <span>حذف جميع البيانات السابقة من الجدول قبل الاستيراد</span>
                            </label>
                        </div>
                        <button type="submit" class="btn btn-success">استيراد بيانات المحاسبة</button>
                    </form>
                </div>
                
                <div class="table-card">
                    <h4>⚖️ طلبات إنهاء النزاع (Conflict)</h4>
                    <p>استيراد بيانات طلبات إنهاء النزاع من ملفات CSV بتنسيق ConflictEXPORT_YYYYMMDD.csv</p>
                    <form method="post" enctype="multipart/form-data" style="margin-top: 15px;">
                        <input type="hidden" name="import_table" value="conflict">
                        <input type="file" name="csv_file" accept=".csv" class="file-input" required>
                        <div style="margin: 10px 0;">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="clear_table" value="1">
                                <span>حذف جميع البيانات السابقة من الجدول قبل الاستيراد</span>
                            </label>
                        </div>
                        <button type="submit" class="btn btn-success">استيراد بيانات إنهاء النزاع</button>
                    </form>
                </div>
                
                <div class="table-card">
                    <h4>🤝 طلبات تسوية النزاع (Dispute)</h4>
                    <p>استيراد بيانات طلبات تسوية النزاع من ملفات CSV بتنسيق DisputeEXPORT_YYYYMMDD.csv</p>
                    <form method="post" enctype="multipart/form-data" style="margin-top: 15px;">
                        <input type="hidden" name="import_table" value="dispute">
                        <input type="file" name="csv_file" accept=".csv" class="file-input" required>
                        <div style="margin: 10px 0;">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="clear_table" value="1">
                                <span>حذف جميع البيانات السابقة من الجدول قبل الاستيراد</span>
                            </label>
                        </div>
                        <button type="submit" class="btn btn-success">استيراد بيانات تسوية النزاع</button>
                    </form>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 40px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="recreate_tables_from_csv.php" class="btn">إنشاء الجداول من CSV</a>
                <a href="dashboard.php" class="btn btn-success">عرض لوحة التحكم</a>
            </div>
        </div>
    </div>
</body>
</html>
