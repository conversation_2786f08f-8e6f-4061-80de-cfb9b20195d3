<?php
/**
 * صفحة إدارة البيانات - TIaF Report System
 * إدارة وصيانة البيانات في الجداول الأساسية
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @date 2025-06-17
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

// معالجة طلبات الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo = getDBConnection();
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'clear_table':
                    $table = $_POST['table'];
                    if (in_array($table, ['accounting', 'conflict', 'dispute'])) {
                        $stmt = $pdo->prepare("DELETE FROM `$table`");
                        $stmt->execute();
                        $message = "تم حذف جميع البيانات من جدول $table بنجاح";
                        $messageType = 'success';
                    }
                    break;
                    
                case 'clear_by_date':
                    $table = $_POST['table'];
                    $date = $_POST['export_date'];
                    if (in_array($table, ['accounting', 'conflict', 'dispute']) && !empty($date)) {
                        $stmt = $pdo->prepare("DELETE FROM `$table` WHERE export_date = ?");
                        $stmt->execute([$date]);
                        $affected = $stmt->rowCount();
                        $message = "تم حذف $affected سجل من جدول $table لتاريخ $date";
                        $messageType = 'success';
                    }
                    break;
                    
                case 'optimize_tables':
                    $tables = ['accounting', 'conflict', 'dispute'];
                    foreach ($tables as $table) {
                        $pdo->exec("OPTIMIZE TABLE `$table`");
                    }
                    $message = "تم تحسين جميع الجداول بنجاح";
                    $messageType = 'success';
                    break;
            }
        }
    } catch (Exception $e) {
        $message = "خطأ: " . $e->getMessage();
        $messageType = 'error';
    }
}

// الحصول على إحصائيات الجداول
try {
    $pdo = getDBConnection();
    $stats = [];
    
    $tables = ['accounting', 'conflict', 'dispute'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM `$table`");
        $stats[$table]['total'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT export_date, COUNT(*) as count FROM `$table` GROUP BY export_date ORDER BY export_date DESC LIMIT 10");
        $stats[$table]['by_date'] = $stmt->fetchAll();
        
        $stmt = $pdo->query("SELECT 
            ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = '$table'");
        $stats[$table]['size'] = $stmt->fetchColumn();
    }
} catch (Exception $e) {
    $stats = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة البيانات - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/main.css">
    <style>
        .danger-zone {
            background: #fff5f5;
            border: 2px solid #fed7d7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .danger-zone h3 {
            color: #c53030;
            margin-bottom: 15px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .stat-item:last-child {
            border-bottom: none;
        }
        
        .date-list {
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h1>🗂️ إدارة البيانات</h1>
                <p>إدارة وصيانة البيانات في الجداول الأساسية</p>
            </div>
            
            <div class="card-body">
                <?php if (isset($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?>">
                    <?php echo $message; ?>
                </div>
                <?php endif; ?>
                
                <div class="stats-grid">
                    <?php foreach ($stats as $tableName => $tableStats): ?>
                    <div class="stat-card">
                        <h4>
                            <?php 
                            $tableNames = [
                                'accounting' => '📊 طلبات المحاسبة',
                                'conflict' => '⚖️ طلبات إنهاء النزاع',
                                'dispute' => '🤝 طلبات تسوية النزاع'
                            ];
                            echo $tableNames[$tableName];
                            ?>
                        </h4>
                        
                        <div class="stat-item">
                            <span>إجمالي السجلات:</span>
                            <strong><?php echo number_format($tableStats['total']); ?></strong>
                        </div>
                        
                        <div class="stat-item">
                            <span>حجم الجدول:</span>
                            <strong><?php echo $tableStats['size']; ?> ميجابايت</strong>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <strong>البيانات حسب تاريخ التصدير:</strong>
                            <div class="date-list">
                                <?php foreach ($tableStats['by_date'] as $dateData): ?>
                                <div class="stat-item">
                                    <span><?php echo formatDate($dateData['export_date']); ?></span>
                                    <span><?php echo number_format($dateData['count']); ?> سجل</span>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="danger-zone">
                    <h3>⚠️ منطقة الخطر - عمليات الحذف</h3>
                    <p><strong>تحذير:</strong> هذه العمليات لا يمكن التراجع عنها. تأكد من عمل نسخة احتياطية قبل المتابعة.</p>
                    
                    <div class="row" style="margin-top: 20px;">
                        <div class="col-4">
                            <h4>حذف جدول كامل</h4>
                            <form method="post" onsubmit="return confirm('هل أنت متأكد من حذف جميع البيانات؟')">
                                <input type="hidden" name="action" value="clear_table">
                                <div class="form-group">
                                    <select name="table" class="form-control" required>
                                        <option value="">اختر الجدول</option>
                                        <option value="accounting">طلبات المحاسبة</option>
                                        <option value="conflict">طلبات إنهاء النزاع</option>
                                        <option value="dispute">طلبات تسوية النزاع</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-danger">حذف الجدول</button>
                            </form>
                        </div>
                        
                        <div class="col-4">
                            <h4>حذف بيانات تاريخ محدد</h4>
                            <form method="post" onsubmit="return confirm('هل أنت متأكد من حذف بيانات هذا التاريخ؟')">
                                <input type="hidden" name="action" value="clear_by_date">
                                <div class="form-group">
                                    <select name="table" class="form-control" required>
                                        <option value="">اختر الجدول</option>
                                        <option value="accounting">طلبات المحاسبة</option>
                                        <option value="conflict">طلبات إنهاء النزاع</option>
                                        <option value="dispute">طلبات تسوية النزاع</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <input type="date" name="export_date" class="form-control" required>
                                </div>
                                <button type="submit" class="btn btn-warning">حذف التاريخ</button>
                            </form>
                        </div>
                        
                        <div class="col-4">
                            <h4>تحسين الجداول</h4>
                            <p>تحسين أداء الجداول وإعادة تنظيم البيانات</p>
                            <form method="post">
                                <input type="hidden" name="action" value="optimize_tables">
                                <button type="submit" class="btn btn-info">تحسين الجداول</button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                    <a href="import_data.php" class="btn btn-success">استيراد البيانات</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
