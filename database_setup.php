<?php
/**
 * ملف إعداد قاعدة البيانات - TIaF Report System
 * إنشاء قاعدة البيانات والجداول المطلوبة
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<h2>🚀 بدء إعداد قاعدة البيانات TIaF Report System</h2>";

    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✅ تم إنشاء قاعدة البيانات: $database</p>";

    // الاتصال بقاعدة البيانات المحددة
    $pdo->exec("USE `$database`");

    // ===== إنشاء الجداول المرجعية =====

    // جدول التصنيف (Classification) - 7 حقول كما هو في ملف CSV
    $sql_classification = "
    CREATE TABLE IF NOT EXISTS `classification` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `classification_type` varchar(50) NOT NULL COMMENT 'التصنيف',
        `office_code` varchar(10) NOT NULL COMMENT 'كود المأمورية',
        `office_name` varchar(255) NOT NULL COMMENT 'اسم المأمورية',
        `region` varchar(100) NOT NULL COMMENT 'المنطقة',
        `office_branch` varchar(255) NOT NULL COMMENT 'المامورية',
        `specialization` varchar(100) NOT NULL COMMENT 'الاختصاص',
        `general_manager` varchar(100) NOT NULL COMMENT 'مديري العموم',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_office_code` (`office_code`),
        KEY `idx_classification_type` (`classification_type`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_classification);
    echo "<p>✅ تم إنشاء جدول التصنيف (classification) - 7 حقول</p>";

    // جدول الوعاء (Container) - 3 حقول كما هو في ملف CSV
    $sql_container = "
    CREATE TABLE IF NOT EXISTS `container` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `container_code` varchar(10) NOT NULL COMMENT 'الوعاء',
        `container_name` varchar(255) NOT NULL COMMENT 'اسم الوعاء',
        `classification` varchar(50) NOT NULL COMMENT 'التصنيف',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_container_code` (`container_code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_container);
    echo "<p>✅ تم إنشاء جدول الوعاء (container) - 3 حقول</p>";

    // جدول حالة الطلب (Request Status) - 2 حقل كما هو في ملف CSV
    $sql_request_status = "
    CREATE TABLE IF NOT EXISTS `request_status` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `status_code` varchar(10) NOT NULL COMMENT 'حالة الطلب',
        `status_description` varchar(255) NOT NULL COMMENT 'وصف الحالة',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_status_code` (`status_code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_request_status);
    echo "<p>✅ تم إنشاء جدول حالة الطلب (request_status) - 2 حقل</p>";

    // ===== إدخال البيانات المرجعية من ملفات CSV =====

    echo "<h3>📥 استيراد البيانات من ملفات Mapping Table</h3>";

    // استيراد بيانات التصنيف من ملف CSV
    $classification_file = 'Mapping Table/جدول التصنيف.csv';
    if (file_exists($classification_file)) {
        $handle = fopen($classification_file, 'r');
        $headers = fgetcsv($handle); // تخطي العنوان الأول

        $stmt = $pdo->prepare("INSERT IGNORE INTO classification (classification_type, office_code, office_name, region, office_branch, specialization, general_manager) VALUES (?, ?, ?, ?, ?, ?, ?)");

        $count = 0;
        while (($data = fgetcsv($handle)) !== FALSE) {
            if (count($data) >= 7) {
                $stmt->execute($data);
                $count++;
            }
        }
        fclose($handle);
        echo "<p>✅ تم إدخال $count سجل من بيانات التصنيف</p>";
    } else {
        echo "<p>⚠️ ملف التصنيف غير موجود</p>";
    }

    // استيراد بيانات الوعاء من ملف CSV
    $container_file = 'Mapping Table/جدول الوعاء.csv';
    if (file_exists($container_file)) {
        $handle = fopen($container_file, 'r');
        $headers = fgetcsv($handle); // تخطي العنوان الأول

        $stmt = $pdo->prepare("INSERT IGNORE INTO container (container_code, container_name, classification) VALUES (?, ?, ?)");

        $count = 0;
        while (($data = fgetcsv($handle)) !== FALSE) {
            if (count($data) >= 3) {
                $stmt->execute($data);
                $count++;
            }
        }
        fclose($handle);
        echo "<p>✅ تم إدخال $count سجل من بيانات الوعاء</p>";
    } else {
        echo "<p>⚠️ ملف الوعاء غير موجود</p>";
    }

    // استيراد بيانات حالة الطلب من ملف CSV
    $status_file = 'Mapping Table/جدول حالة الطلب.csv';
    if (file_exists($status_file)) {
        $handle = fopen($status_file, 'r');
        $headers = fgetcsv($handle); // تخطي العنوان الأول

        $stmt = $pdo->prepare("INSERT IGNORE INTO request_status (status_code, status_description) VALUES (?, ?)");

        $count = 0;
        while (($data = fgetcsv($handle)) !== FALSE) {
            if (count($data) >= 2) {
                $stmt->execute($data);
                $count++;
            }
        }
        fclose($handle);
        echo "<p>✅ تم إدخال $count سجل من بيانات حالة الطلب</p>";
    } else {
        echo "<p>⚠️ ملف حالة الطلب غير موجود</p>";
    }

    // ===== إنشاء الجداول الأساسية =====

    echo "<h3>📊 إنشاء الجداول الأساسية</h3>";

    // حذف الجداول الموجودة أولاً لإعادة إنشائها
    $pdo->exec("DROP TABLE IF EXISTS `accounting`");
    $pdo->exec("DROP TABLE IF EXISTS `conflict`");
    $pdo->exec("DROP TABLE IF EXISTS `dispute`");
    echo "<p>🗑️ تم حذف الجداول الموجودة لإعادة إنشائها</p>";

    // جدول المحاسبة (Accounting) - 23 حقل + export_date (بناءً على AccountingEXPORT_20250526.csv)
    $sql_accounting = "
    CREATE TABLE `accounting` (
        `request_number` varchar(50) COMMENT 'رقم الطلب | Request Number',
        `status_creation_date` datetime COMMENT 'تاريخ إنشاء الحالة | Status Creation Date',
        `created_by` varchar(255) COMMENT 'منشئ الطلب | Created By',
        `modified_by` varchar(255) COMMENT 'معدل الطلب | Modified By',
        `modification_date` datetime COMMENT 'تاريخ التعديل | Modification Date',
        `request_status` varchar(10) COMMENT 'حالة الطلب | Request Status',
        `status_description` varchar(255) COMMENT 'وصف الحالة | Status Description',
        `tax_registration_number` decimal(15,2) COMMENT 'رقم التسجيل الضريبي | Tax Registration Number',
        `office_code` varchar(10) COMMENT 'كود المأمورية | Office Code',
        `office_name` varchar(255) COMMENT 'اسم المأمورية | Office Name',
        `taxpayer_name` varchar(255) COMMENT 'اسم المكلف | Taxpayer Name',
        `address` text COMMENT 'العنوان | Address',
        `email` varchar(255) COMMENT 'البريد الإلكتروني | Email',
        `phone_number` varchar(50) COMMENT 'رقم الهاتف | Phone Number',
        `container` varchar(10) COMMENT 'الوعاء | Container',
        `container_name` varchar(255) COMMENT 'اسم الوعاء | Container Name',
        `period` varchar(255) COMMENT 'الفترة | Period',
        `container_cost` decimal(15,2) COMMENT 'قيمة الوعاء | Container Cost',
        `acquiring_cost` decimal(15,2) COMMENT 'تكلفة اقتناء السهم | Acquiring Cost',
        `container_cost_before` decimal(15,2) COMMENT 'قيمة الوعاء قبل التعديل | Container Cost Before',
        `acquiring_cost_before` decimal(15,2) COMMENT 'تكلفة اقتناء السهم قبل التعديل | Acquiring Cost Before',
        `expected_tax` decimal(15,2) COMMENT 'الضريبة المتوقعة | Expected Tax',
        `tax_according_to_law` decimal(15,2) COMMENT 'الضريبة طبقاً للقانون | Tax According To Law',
        `export_date` date COMMENT 'تاريخ التصدير من اسم الملف | Export Date from Filename',
        KEY `idx_request_number` (`request_number`),
        KEY `idx_office_code` (`office_code`),
        KEY `idx_export_date` (`export_date`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_accounting);
    echo "<p>✅ تم إنشاء جدول المحاسبة (accounting) - 23 حقل + export_date</p>";

    // جدول النزاع (Conflict) - 39 حقل + export_date (بناءً على ConflictEXPORT_20250526.csv)
    $sql_conflict = "
    CREATE TABLE `conflict` (
        `request_number` varchar(50) COMMENT 'رقم الطلب | Request Number',
        `status_creation_date` datetime COMMENT 'تاريخ إنشاء الطلب | Status Creation Date',
        `created_by` varchar(255) COMMENT 'منشئ الطلب | Created By',
        `modification_date` datetime COMMENT 'تاريخ التعديل | Modification Date',
        `modified_by` varchar(255) COMMENT 'معدل الطلب | Modified By',
        `request_status` varchar(10) COMMENT 'حالة الطلب | Request Status',
        `status_description` varchar(255) COMMENT 'وصف حالة الطلب | Status Description',
        `request_type` varchar(10) COMMENT 'نوع الطلب | Request Type',
        `request_type_name` varchar(255) COMMENT 'اسم نوع الطلب | Request Type Name',
        `tax_registration_number` decimal(15,2) COMMENT 'رقم التسجيل الضريبي | Tax Registration Number',
        `office_code` varchar(10) COMMENT 'كود المأمورية | Office Code',
        `office_name` varchar(255) COMMENT 'اسم المأمورية | Office Name',
        `taxpayer_name` varchar(255) COMMENT 'اسم المكلف | Taxpayer Name',
        `address` text COMMENT 'العنوان | Address',
        `phone_number` varchar(50) COMMENT 'رقم الهاتف | Phone Number',
        `email` varchar(255) COMMENT 'البريد الإلكتروني | Email',
        `accounting_type` varchar(10) COMMENT 'نوع المحاسبة | Accounting Type',
        `accounting_type_description` varchar(255) COMMENT 'وصف نوع المحاسبة | Accounting Type Description',
        `container` varchar(10) COMMENT 'الوعاء | Container',
        `container_name` varchar(255) COMMENT 'اسم الوعاء | Container Name',
        `period` varchar(255) COMMENT 'الفترة | Period',
        `dispute_stage` varchar(10) COMMENT 'مرحلة النزاع | Dispute Stage',
        `dispute_stage_description` varchar(255) COMMENT 'وصف مرحلة النزاع | Dispute Stage Description',
        `case_number` varchar(50) COMMENT 'رقم الدعوي / الطعن | Case Number',
        `dispute_authority` varchar(10) COMMENT 'جهة نظر النزاع | Dispute Authority',
        `dispute_authority_name` varchar(255) COMMENT 'اسم جهة نظر النزاع | Dispute Authority Name',
        `other_authority` varchar(255) COMMENT 'جهة نظر أخرى | Other Authority',
        `tax_according_to_declaration` decimal(15,2) COMMENT 'الضريبة طبقا للاقرار | Tax According To Declaration',
        `tax_from_last_assessment` decimal(15,2) COMMENT 'الضريبة من اخر ربط | Tax From Last Assessment',
        `last_assessment_year` varchar(10) COMMENT 'سنه آخر ربط | Last Assessment Year',
        `tax_according_to_form` decimal(15,2) COMMENT 'الضريبة طبقا للنموذج | Tax According To Form',
        `tax_paid` decimal(15,2) COMMENT 'الضريبه المسددة | Tax Paid',
        `tax_according_to_declaration_before_modification` decimal(15,2) COMMENT 'الضريبة طبقا للاقرار قبل التعديل | Tax According To Declaration Before Modification',
        `tax_from_last_assessment_before_modification` decimal(15,2) COMMENT 'الضريبة من اخر ربط قبل التعديل | Tax From Last Assessment Before Modification',
        `tax_according_to_form_before_modification` decimal(15,2) COMMENT 'الضريبة طبقا للنموذج قبل التعديل | Tax According To Form Before Modification',
        `tax_paid_before_modification` decimal(15,2) COMMENT 'الضريبه المسددة قبل التعديل | Tax Paid Before Modification',
        `expected_tax` decimal(15,2) COMMENT 'الضريبه المتوقعة | Expected Tax',
        `tax_according_to_law` decimal(15,2) COMMENT 'الضريبة طبقا للقانون | Tax According To Law',
        `tax_due_payment` decimal(15,2) COMMENT 'الضريبه المستحقه السداد | Tax Due Payment',
        `export_date` date COMMENT 'تاريخ التصدير من اسم الملف | Export Date from Filename',
        KEY `idx_request_number` (`request_number`),
        KEY `idx_office_code` (`office_code`),
        KEY `idx_export_date` (`export_date`),
        KEY `idx_case_number` (`case_number`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_conflict);
    echo "<p>✅ تم إنشاء جدول النزاع (conflict) - 39 حقل + export_date</p>";

    // جدول المنازعة (Dispute) - 39 حقل + export_date (بناءً على DisputeEXPORT_20250526.csv)
    $sql_dispute = "
    CREATE TABLE `dispute` (
        `request_number` varchar(50) COMMENT 'رقم الطلب | Request Number',
        `status_creation_date` datetime COMMENT 'تاريخ إنشاء الطلب | Status Creation Date',
        `created_by` varchar(255) COMMENT 'منشئ الطلب | Created By',
        `modification_date` datetime COMMENT 'تاريخ التعديل | Modification Date',
        `modified_by` varchar(255) COMMENT 'معدل الطلب | Modified By',
        `request_status` varchar(10) COMMENT 'حالة الطلب | Request Status',
        `status_description` varchar(255) COMMENT 'وصف الحالة | Status Description',
        `request_type` varchar(10) COMMENT 'نوع الطلب | Request Type',
        `request_type_name` varchar(255) COMMENT 'اسم نوع الطلب | Request Type Name',
        `tax_registration_number` decimal(15,2) COMMENT 'رقم التسجيل الضريبي | Tax Registration Number',
        `office_code` varchar(10) COMMENT 'كود المأمورية | Office Code',
        `office_name` varchar(255) COMMENT 'اسم المأمورية | Office Name',
        `taxpayer_name` varchar(255) COMMENT 'اسم المكلف | Taxpayer Name',
        `address` text COMMENT 'العنوان | Address',
        `phone_number` varchar(50) COMMENT 'رقم الهاتف | Phone Number',
        `email` varchar(255) COMMENT 'البريد الإلكتروني | Email',
        `accounting_type` varchar(10) COMMENT 'نوع المحاسبة | Accounting Type',
        `accounting_type_description` varchar(255) COMMENT 'وصف نوع المحاسبة | Accounting Type Description',
        `container` varchar(10) COMMENT 'الوعاء | Container',
        `container_name` varchar(255) COMMENT 'وصف الوعاء | Container Name',
        `period` varchar(255) COMMENT 'الفترة | Period',
        `dispute_stage` varchar(10) COMMENT 'مرحله النزاع | Dispute Stage',
        `dispute_stage_description` varchar(255) COMMENT 'اسم مرحلة النزاع | Dispute Stage Description',
        `case_number` varchar(50) COMMENT 'رقم الدعوي / الطعن | Case Number',
        `dispute_authority` varchar(10) COMMENT 'جهة نظر النزاع | Dispute Authority',
        `dispute_authority_name` varchar(255) COMMENT 'اسم جهة نظر النزاع | Dispute Authority Name',
        `other_authority` varchar(255) COMMENT 'جهة نظر أخرى | Other Authority',
        `tax_according_to_declaration` decimal(15,2) COMMENT 'الضريبة طبقا للاقرار | Tax According To Declaration',
        `tax_from_last_assessment` decimal(15,2) COMMENT 'الضريبة من اخر ربط | Tax From Last Assessment',
        `last_assessment_year` varchar(10) COMMENT 'سنه آخر ربط | Last Assessment Year',
        `tax_according_to_form` decimal(15,2) COMMENT 'الضريبة طبقا للنموذج | Tax According To Form',
        `tax_paid` decimal(15,2) COMMENT 'الضريبه المسددة | Tax Paid',
        `tax_according_to_declaration_before_modification` decimal(15,2) COMMENT 'الضريبة طبقا للاقرار قبل التعديل | Tax According To Declaration Before Modification',
        `tax_from_last_assessment_before_modification` decimal(15,2) COMMENT 'الضريبة من اخر ربط قبل التعديل | Tax From Last Assessment Before Modification',
        `tax_according_to_form_before_modification` decimal(15,2) COMMENT 'الضريبة طبقا للنموذج قبل التعديل | Tax According To Form Before Modification',
        `tax_paid_before_modification` decimal(15,2) COMMENT 'الضريبه المسددة قبل التعديل | Tax Paid Before Modification',
        `expected_tax` decimal(15,2) COMMENT 'الضريبه المتوقعة | Expected Tax',
        `tax_according_to_law` decimal(15,2) COMMENT 'الضريبة طبقا للقانون | Tax According To Law',
        `tax_due_payment` decimal(15,2) COMMENT 'الضريبه المستحقه السداد | Tax Due Payment',
        `export_date` date COMMENT 'تاريخ التصدير من اسم الملف | Export Date from Filename',
        KEY `idx_request_number` (`request_number`),
        KEY `idx_office_code` (`office_code`),
        KEY `idx_export_date` (`export_date`),
        KEY `idx_case_number` (`case_number`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_dispute);
    echo "<p>✅ تم إنشاء جدول المنازعة (dispute) - 39 حقل + export_date</p>";

    echo "<h3>🎉 تم إعادة إنشاء الجداول الثلاثة بنجاح!</h3>";
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📋 ملخص الجداول المُعاد إنشاؤها:</h4>";
    echo "<ul>";
    echo "<li><strong>الجداول المرجعية (لم تتغير):</strong>";
    echo "<ul>";
    echo "<li>classification - جدول التصنيف (7 حقول)</li>";
    echo "<li>container - جدول الوعاء (3 حقول)</li>";
    echo "<li>request_status - جدول حالة الطلب (2 حقل)</li>";
    echo "</ul></li>";
    echo "<li><strong>الجداول الأساسية (تم إعادة إنشاؤها):</strong>";
    echo "<ul>";
    echo "<li><strong>accounting</strong> - جدول المحاسبة (23 حقل + export_date) - بدون مفاتيح أساسية</li>";
    echo "<li><strong>conflict</strong> - جدول النزاع (39 حقل + export_date) - بدون مفاتيح أساسية</li>";
    echo "<li><strong>dispute</strong> - جدول المنازعة (39 حقل + export_date) - بدون مفاتيح أساسية</li>";
    echo "</ul></li>";
    echo "</ul>";
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
    echo "<h5>🔑 ملاحظات مهمة:</h5>";
    echo "<ul>";
    echo "<li><strong>بدون مفاتيح أساسية:</strong> الجداول الثلاثة لا تحتوي على مفاتيح أساسية أو قيود لأنها ستستقبل بيانات مستمرة</li>";
    echo "<li><strong>حقل export_date:</strong> يتم ملؤه تلقائياً من اسم الملف المستورد</li>";
    echo "<li><strong>مثال:</strong> AccountingEXPORT_20250526 → export_date = 2025-05-26</li>";
    echo "<li><strong>الحقول:</strong> تطابق تماماً ما هو موجود في ملفات CSV المرجعية</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    die();
}
?>