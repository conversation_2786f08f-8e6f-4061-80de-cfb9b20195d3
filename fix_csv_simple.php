<?php
/**
 * أداة إصلاح ملفات CSV البسيطة - TIaF Report System
 * إزالة BOM وتنظيف أسماء الأعمدة (بدون جلسات)
 * 
 * <AUTHOR> Development Team
 * @version 1.4.2
 * @date 2025-06-17
 */

// معالجة رفع الملف
$processed = false;
$fixedContent = '';
$originalFileName = '';
$fixedFileName = '';
$fileInfo = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    $file = $_FILES['csv_file'];
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        $processed = true;
        $originalFileName = $file['name'];
        
        // قراءة الملف الأصلي
        $originalContent = file_get_contents($file['tmp_name']);
        
        // إزالة BOM
        $cleanContent = str_replace("\xEF\xBB\xBF", '', $originalContent);
        
        // تحليل CSV
        $lines = explode("\n", $cleanContent);
        $headers = str_getcsv($lines[0]);
        
        // معلومات الملف
        $fileInfo = [
            'name' => $originalFileName,
            'size' => $file['size'],
            'lines' => count($lines),
            'columns' => count($headers),
            'has_bom' => strpos($originalContent, "\xEF\xBB\xBF") !== false
        ];
        
        // تنظيف أسماء الأعمدة
        $cleanHeaders = [];
        foreach ($headers as $header) {
            $cleanHeader = trim(str_replace("\xEF\xBB\xBF", '', $header));
            $cleanHeaders[] = $cleanHeader;
        }
        
        // إنشاء الملف المُصحح
        $fixedLines = [];
        $fixedLines[] = implode(',', array_map(function($header) {
            return '"' . str_replace('"', '""', $header) . '"';
        }, $cleanHeaders));
        
        // إضافة باقي الأسطر
        for ($i = 1; $i < count($lines); $i++) {
            if (trim($lines[$i]) !== '') {
                $fixedLines[] = $lines[$i];
            }
        }
        
        $fixedContent = implode("\n", $fixedLines);
        $fixedFileName = 'fixed_' . $originalFileName;
    }
}

// معالجة التحميل
if (isset($_POST['download']) && isset($_POST['fixed_content'])) {
    $content = base64_decode($_POST['fixed_content']);
    $filename = $_POST['filename'] ?? 'fixed_file.csv';

    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . strlen($content));
    header('Cache-Control: private, max-age=0, must-revalidate');
    header('Pragma: public');

    echo $content;
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح ملفات CSV - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
        }
        
        .file-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 0.9rem;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح ملفات CSV</h1>
            <p>أداة لإزالة BOM وتنظيف أسماء الأعمدة في ملفات CSV</p>
        </div>
        
        <div class="content">
            <?php if ($processed): ?>
                <div class="section">
                    <h3>🔄 نتائج المعالجة</h3>
                    
                    <h4>📋 معلومات الملف الأصلي:</h4>
                    <div class="file-info">
                        <p><strong>اسم الملف:</strong> <?php echo htmlspecialchars($fileInfo['name']); ?></p>
                        <p><strong>الحجم:</strong> <?php echo number_format($fileInfo['size']); ?> بايت</p>
                        <p><strong>عدد الأسطر:</strong> <?php echo $fileInfo['lines']; ?></p>
                        <p><strong>عدد الأعمدة:</strong> <?php echo $fileInfo['columns']; ?></p>
                        <p><strong>يحتوي على BOM:</strong> <?php echo $fileInfo['has_bom'] ? 'نعم ❌' : 'لا ✅'; ?></p>
                    </div>
                    
                    <?php if ($fileInfo['has_bom']): ?>
                    <div class="section warning">
                        <h4>⚠️ تم العثور على BOM</h4>
                        <p>تم العثور على Byte Order Mark في بداية الملف. سيتم إزالته تلقائياً.</p>
                    </div>
                    <?php endif; ?>
                    
                    <h4>🔍 أسماء الأعمدة:</h4>
                    <table>
                        <tr><th>الرقم</th><th>قبل التنظيف</th><th>بعد التنظيف</th></tr>
                        <?php
                        $originalHeaders = str_getcsv(explode("\n", file_get_contents($_FILES['csv_file']['tmp_name']))[0]);
                        $cleanHeaders = [];
                        foreach ($originalHeaders as $index => $header) {
                            $cleanHeader = trim(str_replace("\xEF\xBB\xBF", '', $header));
                            $cleanHeaders[] = $cleanHeader;
                            
                            echo "<tr>";
                            echo "<td>" . ($index + 1) . "</td>";
                            echo "<td>" . htmlspecialchars($header) . "</td>";
                            echo "<td>" . htmlspecialchars($cleanHeader) . "</td>";
                            echo "</tr>";
                        }
                        ?>
                    </table>
                </div>
                
                <div class="section success">
                    <h4>✅ تم إصلاح الملف بنجاح!</h4>
                    <p><strong>الملف الجديد:</strong> <?php echo htmlspecialchars($fixedFileName); ?></p>
                    <p><strong>الحجم الجديد:</strong> <?php echo number_format(strlen($fixedContent)); ?> بايت</p>
                    <p><strong>التغييرات المطبقة:</strong></p>
                    <ul>
                        <li>تم إزالة BOM (Byte Order Mark)</li>
                        <li>تم تنظيف أسماء الأعمدة</li>
                        <li>تم إزالة الأسطر الفارغة</li>
                    </ul>
                    
                    <!-- نموذج التحميل -->
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="download" value="1">
                        <input type="hidden" name="fixed_content" value="<?php echo base64_encode($fixedContent); ?>">
                        <input type="hidden" name="filename" value="<?php echo htmlspecialchars($fixedFileName); ?>">
                        <button type="submit" class="btn btn-success">📥 تحميل الملف المُصحح</button>
                    </form>
                </div>
                
                <div class="section">
                    <h4>🔄 معالجة ملف آخر</h4>
                    <a href="fix_csv_simple.php" class="btn">إصلاح ملف جديد</a>
                </div>
                
            <?php else: ?>
                <!-- نموذج رفع الملف -->
                <div class="section">
                    <h3>📤 رفع ملف CSV للإصلاح</h3>
                    <p>ارفع ملف CSV لإزالة BOM وتنظيف أسماء الأعمدة:</p>
                    
                    <form method="post" enctype="multipart/form-data">
                        <div class="upload-area">
                            <p>📁 اختر ملف CSV</p>
                            <input type="file" name="csv_file" accept=".csv" required>
                        </div>
                        <button type="submit" class="btn">🔧 إصلاح الملف</button>
                    </form>
                </div>
                
                <!-- معلومات مفيدة -->
                <div class="section warning">
                    <h3>💡 معلومات مفيدة</h3>
                    <h4>ما هو BOM؟</h4>
                    <p>BOM (Byte Order Mark) هو تسلسل من البايتات يُضاف في بداية ملفات النص لتحديد ترميز الملف. يمكن أن يسبب مشاكل في استيراد البيانات.</p>
                    
                    <h4>متى تحتاج لاستخدام هذه الأداة؟</h4>
                    <ul>
                        <li>عندما تظهر رسائل خطأ مثل "Unknown column '﻿رقم الطلب'"</li>
                        <li>عندما تجد رموز غريبة في بداية أسماء الأعمدة</li>
                        <li>عندما يفشل استيراد ملف CSV بدون سبب واضح</li>
                    </ul>
                    
                    <h4>نصائح:</h4>
                    <ul>
                        <li>احفظ ملفات CSV بترميز UTF-8 بدون BOM</li>
                        <li>تجنب المسافات الزائدة في أسماء الأعمدة</li>
                        <li>استخدم محرر نصوص متقدم مثل Notepad++</li>
                    </ul>
                </div>
            <?php endif; ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="import_data.php" class="btn">استيراد البيانات</a>
                <a href="debug_import.php" class="btn">تشخيص الاستيراد</a>
            </div>
        </div>
    </div>
</body>
</html>
