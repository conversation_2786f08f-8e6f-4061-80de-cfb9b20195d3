<?php
// صفحة استعراض تحليل ملفات Mapping Table بشكل جدولي ثنائي اللغة
// آخر تحديث: 17 يونيو 2025
$pageTitle = 'تحليل جداول Mapping Table';
include 'header.php';
?><!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تحليل جداول Mapping Table</title>
    <style>
        body { font-family: Tahoma, Arial, sans-serif; background: #f9f9f9; color: #222; margin: 0; padding: 0; }
        .container { max-width: 900px; margin: 40px auto; background: #fff; border-radius: 10px; box-shadow: 0 2px 8px #ccc; padding: 32px; }
        h1, h2, h3 { color: #1a237e; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 32px; }
        th, td { border: 1px solid #bbb; padding: 8px 12px; text-align: right; }
        th { background: #e3e6f3; }
        .note { color: #555; font-size: 15px; margin-bottom: 20px; }
        ul { margin: 0 0 16px 0; padding-right: 20px; }
    </style>
</head>
<body>
<div class="container">
    <h1>تحليل جداول Mapping Table</h1>
    <div class="note">كل جدول يعرض الحقول باللغتين العربية والإنجليزية، نوع الحقل، ومثال على القيم.</div>

    <h2>1. جدول التصنيف.csv</h2>
    <table>
        <tr>
            <th>اسم الحقل (عربي)</th>
            <th>Field Name (EN)</th>
            <th>نوع الحقل</th>
            <th>مثال</th>
        </tr>
        <tr><td>التصنيف</td><td>classification</td><td>نص</td><td>الدخل</td></tr>
        <tr><td>كود المأمورية</td><td>office_code</td><td>رقم</td><td>240</td></tr>
        <tr><td>اسم المأمورية</td><td>office_name</td><td>نص</td><td>الاستثمار الاسكندريه</td></tr>
        <tr><td>المنطقة</td><td>region</td><td>نص</td><td>الاسكندرية أول</td></tr>
        <tr><td>المامورية</td><td>office</td><td>نص</td><td>استثمار الاسكندرية</td></tr>
        <tr><td>الاختصاص</td><td>specialization</td><td>نص</td><td>محمد عيد</td></tr>
        <tr><td>مديري العموم</td><td>general_managers</td><td>نص</td><td>علاء مصطفى</td></tr>
    </table>
    <div class="note">كل صف يمثل مأمورية ضريبية مع تصنيفها ومعلوماتها الإدارية. يمكن الربط مع جداول أخرى عبر كود المأمورية أو اسم المأمورية.</div>

    <h2>2. جدول الوعاء.csv</h2>
    <table>
        <tr>
            <th>اسم الحقل (عربي)</th>
            <th>Field Name (EN)</th>
            <th>نوع الحقل</th>
            <th>مثال</th>
        </tr>
        <tr><td>الوعاء</td><td>container_id</td><td>رقم تسلسلي</td><td>1</td></tr>
        <tr><td>اسم الوعاء</td><td>container_name</td><td>نص</td><td>الدخل</td></tr>
        <tr><td>التصنيف</td><td>classification</td><td>نص</td><td>الدخل</td></tr>
    </table>
    <div class="note">يحدد أنواع الأوعية الضريبية وتصنيفها. يمكن الربط مع جدول التصنيف عبر عمود التصنيف.</div>

    <h2>3. جدول حالة الطلب.csv</h2>
    <table>
        <tr>
            <th>اسم الحقل (عربي)</th>
            <th>Field Name (EN)</th>
            <th>نوع الحقل</th>
            <th>مثال</th>
        </tr>
        <tr><td>حالة الطلب</td><td>request_status</td><td>رقم</td><td>20</td></tr>
        <tr><td>وصف الحالة</td><td>status_description</td><td>نص</td><td>قيد المراجعة</td></tr>
    </table>
    <div class="note">يحدد حالات الطلبات المختلفة ووصفها. يستخدم للربط مع جداول الطلبات الرئيسية عبر رقم الحالة.</div>

    <h2>جدول الربط بين الجداول (العلاقات المرجعية)</h2>
    <table>
        <tr>
            <th>الجدول الرئيسي</th>
            <th>الحقل في الجدول الرئيسي</th>
            <th>الجدول المرجعي</th>
            <th>الحقل في الجدول المرجعي</th>
            <th>الوصف</th>
        </tr>
        <tr>
            <td>Main Table (Accounting, Conflict, Dispute)</td>
            <td>status_id / request_status</td>
            <td>جدول حالة الطلب</td>
            <td>حالة الطلب</td>
            <td>ربط حالة الطلب بالوصف العربي</td>
        </tr>
        <tr>
            <td>Main Table (Accounting, Conflict, Dispute)</td>
            <td>office_code</td>
            <td>جدول التصنيف</td>
            <td>كود المأمورية</td>
            <td>ربط الطلب بالمأمورية/التصنيف الضريبي</td>
        </tr>
        <tr>
            <td>Main Table (Accounting, Conflict, Dispute)</td>
            <td>container_id</td>
            <td>جدول الوعاء</td>
            <td>الوعاء</td>
            <td>ربط الطلب بنوع الوعاء الضريبي</td>
        </tr>
        <tr>
            <td>جدول الوعاء</td>
            <td>التصنيف</td>
            <td>جدول التصنيف</td>
            <td>التصنيف</td>
            <td>ربط نوع الوعاء بالتصنيف الضريبي</td>
        </tr>
    </table>
    <div class="note">
        يوضح هذا الجدول الحقول التي يتم من خلالها الربط بين الجداول الرئيسية والمرجعية، وهو أساس التكامل المرجعي في قاعدة البيانات أو النظام.
    </div>

    <h2>خلاصة</h2>
    <div class="note">
        جداول Mapping Table هي جداول مرجعية (Lookup Tables) تستخدم لتوحيد القيم وربطها بجداول المعاملات الرئيسية. يجب عند بناء قاعدة البيانات أو الموقع استخدام هذه الجداول لضمان سلامة البيانات وتسهيل عمليات البحث والتقارير.
    </div>
</div>
</body>
</html>
<?php include 'footer.php'; ?>
