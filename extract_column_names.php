<?php
/**
 * استخراج أسماء الأعمدة من ملفات CSV - TIaF Report System
 * لإنشاء جداول قاعدة البيانات بأسماء الحقول الصحيحة
 * 
 * <AUTHOR> Development Team
 * @version 1.4.4
 * @date 2025-06-17
 */

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استخراج أسماء الأعمدة - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 0.9rem;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
        }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 0.9rem;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 استخراج أسماء الأعمدة</h1>
            <p>استخراج أسماء الأعمدة الصحيحة من ملفات CSV لإنشاء الجداول</p>
        </div>
        
        <div class="content">
            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_files'])) {
                $files = $_FILES['csv_files'];
                
                echo "<div class='section'>";
                echo "<h3>📊 نتائج استخراج أسماء الأعمدة</h3>";
                
                $allColumns = [];
                
                for ($i = 0; $i < count($files['name']); $i++) {
                    if ($files['error'][$i] === UPLOAD_ERR_OK) {
                        $fileName = $files['name'][$i];
                        $tmpName = $files['tmp_name'][$i];
                        
                        echo "<h4>📁 ملف: " . htmlspecialchars($fileName) . "</h4>";
                        
                        // تحديد نوع الجدول من اسم الملف
                        $tableType = 'unknown';
                        if (strpos($fileName, 'Accounting') !== false) {
                            $tableType = 'accounting';
                        } elseif (strpos($fileName, 'Conflict') !== false) {
                            $tableType = 'conflict';
                        } elseif (strpos($fileName, 'Dispute') !== false) {
                            $tableType = 'dispute';
                        }
                        
                        // قراءة العناوين
                        $handle = fopen($tmpName, 'r');
                        $headers = fgetcsv($handle);
                        
                        // تنظيف العناوين
                        $cleanHeaders = array_map(function($header) {
                            return trim(str_replace("\xEF\xBB\xBF", '', $header));
                        }, $headers);
                        
                        $allColumns[$tableType] = $cleanHeaders;
                        
                        echo "<p><strong>نوع الجدول:</strong> $tableType</p>";
                        echo "<p><strong>عدد الأعمدة:</strong> " . count($cleanHeaders) . "</p>";
                        
                        // عرض العناوين
                        echo "<h5>أسماء الأعمدة:</h5>";
                        echo "<table>";
                        echo "<tr><th>الرقم</th><th>اسم العمود</th><th>اسم الحقل المقترح</th></tr>";
                        
                        foreach ($cleanHeaders as $index => $header) {
                            $fieldName = createFieldName($header);
                            
                            echo "<tr>";
                            echo "<td>" . ($index + 1) . "</td>";
                            echo "<td>" . htmlspecialchars($header) . "</td>";
                            echo "<td>" . htmlspecialchars($fieldName) . "</td>";
                            echo "</tr>";
                        }
                        
                        echo "</table>";
                        fclose($handle);
                        echo "<hr>";
                    }
                }
                
                // إنشاء SQL للجداول
                if (!empty($allColumns)) {
                    echo "<h3>📝 SQL لإنشاء الجداول</h3>";
                    
                    foreach ($allColumns as $tableName => $columns) {
                        echo "<h4>جدول $tableName:</h4>";
                        echo "<pre>";
                        echo generateCreateTableSQL($tableName, $columns);
                        echo "</pre>";
                    }
                    
                    // إنشاء دالة تحويل أسماء الأعمدة
                    echo "<h3>🔄 دالة تحويل أسماء الأعمدة</h3>";
                    echo "<pre>";
                    echo generateColumnMappingFunction($allColumns);
                    echo "</pre>";
                }
                
                echo "</div>";
            }
            
            // دالة لإنشاء اسم الحقل من اسم العمود
            function createFieldName($columnName) {
                // إزالة الرموز الخاصة والمسافات
                $fieldName = $columnName;
                $fieldName = str_replace([' ', '-', '/', '\\', '(', ')', '[', ']', '.', ',', ':', ';'], '_', $fieldName);
                $fieldName = preg_replace('/[^\p{L}\p{N}_]/u', '_', $fieldName);
                $fieldName = preg_replace('/_+/', '_', $fieldName);
                $fieldName = trim($fieldName, '_');
                $fieldName = strtolower($fieldName);
                
                // إذا كان الاسم فارغ أو يبدأ برقم، أضف بادئة
                if (empty($fieldName) || is_numeric($fieldName[0])) {
                    $fieldName = 'field_' . $fieldName;
                }
                
                return $fieldName;
            }
            
            // دالة لتحديد نوع البيانات
            function getDataType($columnName) {
                $columnLower = strtolower($columnName);
                
                if (strpos($columnLower, 'تاريخ') !== false || strpos($columnLower, 'date') !== false) {
                    return 'datetime';
                } elseif (strpos($columnLower, 'ضريبة') !== false || strpos($columnLower, 'tax') !== false || 
                         strpos($columnLower, 'مبلغ') !== false || strpos($columnLower, 'amount') !== false) {
                    return 'decimal(15,2)';
                } elseif (strpos($columnLower, 'رقم') !== false && strpos($columnLower, 'هاتف') === false) {
                    return 'varchar(50)';
                } elseif (strpos($columnLower, 'كود') !== false || strpos($columnLower, 'code') !== false) {
                    return 'varchar(10)';
                } elseif (strpos($columnLower, 'عنوان') !== false || strpos($columnLower, 'address') !== false) {
                    return 'text';
                } elseif (strpos($columnLower, 'وصف') !== false || strpos($columnLower, 'description') !== false) {
                    return 'varchar(255)';
                } else {
                    return 'varchar(255)';
                }
            }
            
            // دالة لإنشاء SQL
            function generateCreateTableSQL($tableName, $columns) {
                $sql = "CREATE TABLE `$tableName` (\n";
                
                foreach ($columns as $column) {
                    $fieldName = createFieldName($column);
                    $dataType = getDataType($column);
                    $sql .= "    `$fieldName` $dataType COMMENT '" . addslashes($column) . "',\n";
                }
                
                $sql .= "    `export_date` date NOT NULL COMMENT 'تاريخ التصدير',\n";
                $sql .= "    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,\n";
                $sql .= "    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n";
                $sql .= "    \n";
                $sql .= "    KEY `idx_export_date` (`export_date`)\n";
                $sql .= ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci\n";
                $sql .= "COMMENT='جدول $tableName - " . count($columns) . " حقل + export_date';";
                
                return $sql;
            }
            
            // دالة لإنشاء دالة تحويل أسماء الأعمدة
            function generateColumnMappingFunction($allColumns) {
                $php = "function getColumnMapping() {\n";
                $php .= "    return [\n";
                
                foreach ($allColumns as $tableName => $columns) {
                    $php .= "        // أعمدة جدول $tableName\n";
                    foreach ($columns as $column) {
                        $fieldName = createFieldName($column);
                        $php .= "        '" . addslashes($column) . "' => '$fieldName',\n";
                    }
                    $php .= "\n";
                }
                
                $php .= "    ];\n";
                $php .= "}";
                
                return $php;
            }
            ?>
            
            <!-- نموذج رفع الملفات -->
            <div class="section">
                <h3>📤 رفع ملفات CSV</h3>
                <p>ارفع ملفات AccountingEXPORT، ConflictEXPORT، و DisputeEXPORT لاستخراج أسماء الأعمدة:</p>
                
                <form method="post" enctype="multipart/form-data">
                    <div class="upload-area">
                        <p>📁 اختر ملفات CSV (يمكن اختيار عدة ملفات)</p>
                        <input type="file" name="csv_files[]" accept=".csv" multiple required>
                        <p><small>اختر ملفات: AccountingEXPORT_*.csv, ConflictEXPORT_*.csv, DisputeEXPORT_*.csv</small></p>
                    </div>
                    <button type="submit" class="btn">📋 استخراج أسماء الأعمدة</button>
                </form>
            </div>
            
            <!-- معلومات مفيدة -->
            <div class="section warning">
                <h3>💡 كيفية الاستخدام</h3>
                <ol>
                    <li><strong>ارفع ملفات CSV الأصلية</strong> التي تريد استيراد البيانات منها</li>
                    <li><strong>راجع أسماء الأعمدة المستخرجة</strong> وأسماء الحقول المقترحة</li>
                    <li><strong>انسخ SQL المُنشأ</strong> وقم بتشغيله لإنشاء الجداول</li>
                    <li><strong>انسخ دالة تحويل الأسماء</strong> وضعها في ملف import_data.php</li>
                    <li><strong>قم بالاستيراد</strong> باستخدام الجداول الجديدة</li>
                </ol>
                
                <h4>الهدف:</h4>
                <p>إنشاء جداول قاعدة البيانات بأسماء حقول تطابق تماماً أسماء الأعمدة في ملفات CSV لتجنب أخطاء الاستيراد.</p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="import_data.php" class="btn">استيراد البيانات</a>
                <a href="recreate_main_tables_correct.php" class="btn">إنشاء الجداول</a>
            </div>
        </div>
    </div>
</body>
</html>
