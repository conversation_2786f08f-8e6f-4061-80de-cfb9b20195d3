<?php
/**
 * لوحة التحكم الرئيسية - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.3
 * @date 2025-06-17
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

// الحصول على الإحصائيات العامة
try {
    $pdo = getDBConnection();
    
    // إحصائيات الجداول الأساسية
    $stats = [];
    $tables = ['accounting', 'conflict', 'dispute'];
    
    foreach ($tables as $table) {
        // إحصائيات مع شرط تصنيف الوعاء
        $stmt = $pdo->query("
            SELECT COUNT(*)
            FROM `$table` a
            LEFT JOIN classification c ON a.office_code = c.office_code
            WHERE ((a.container IN ('1', '3', '4', '5', '6', '7', '8') AND c.classification_type = 'الدخل') OR (a.container = '2' AND c.classification_type = 'القيمة المضافة'))
        ");
        $stats[$table]['total'] = $stmt->fetchColumn();

        // إحصائيات حسب الحالة مع شرط تصنيف الوعاء
        $stmt = $pdo->query("
            SELECT a.request_status, COUNT(*) as count
            FROM `$table` a
            LEFT JOIN classification c ON a.office_code = c.office_code
            WHERE ((a.container IN ('1', '3', '4', '5', '6', '7', '8') AND c.classification_type = 'الدخل') OR (a.container = '2' AND c.classification_type = 'القيمة المضافة'))
            GROUP BY a.request_status
            ORDER BY count DESC
        ");
        $stats[$table]['by_status'] = $stmt->fetchAll();

        // إحصائيات حسب تاريخ التصدير (آخر 12 شهر) مع شرط تصنيف الوعاء
        $stmt = $pdo->query("
            SELECT DATE_FORMAT(a.export_date, '%Y-%m') as month, COUNT(*) as count
            FROM `$table` a
            LEFT JOIN classification c ON a.office_code = c.office_code
            WHERE a.export_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
            AND ((a.container IN ('1', '3', '4', '5', '6', '7', '8') AND c.classification_type = 'الدخل') OR (a.container = '2' AND c.classification_type = 'القيمة المضافة'))
            GROUP BY DATE_FORMAT(a.export_date, '%Y-%m')
            ORDER BY month DESC
            LIMIT 12
        ");
        $stats[$table]['by_month'] = $stmt->fetchAll();
    }
    
    // إحصائيات من الجداول المساعدة
    $stmt = $pdo->query("SELECT COUNT(*) FROM classification");
    $classification_count = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM container");
    $container_count = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM request_status");
    $status_count = $stmt->fetchColumn();
    
    // إحصائيات حسب المنطقة
    $stmt = $pdo->query("
        SELECT region, COUNT(*) as count 
        FROM classification 
        GROUP BY region 
        ORDER BY count DESC 
        LIMIT 10
    ");
    $regions_stats = $stmt->fetchAll();
    
    // إحصائيات حسب مديري العموم
    $stmt = $pdo->query("
        SELECT general_manager, COUNT(*) as count 
        FROM classification 
        GROUP BY general_manager 
        ORDER BY count DESC 
        LIMIT 10
    ");
    $managers_stats = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/main.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .kpi-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .kpi-card:hover {
            transform: translateY(-5px);
        }
        
        .kpi-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .kpi-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            height: 400px;
        }
        
        .chart-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .dark-mode .chart-container {
            background: #34495e;
            color: #ecf0f1;
        }
        
        .dark-mode .chart-title {
            color: #ecf0f1;
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .theme-toggle:hover {
            transform: scale(1.1);
        }
        
        .summary-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .dark-mode .summary-section {
            background: #2c3e50;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()" id="themeToggle">🌙</button>
    
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h1>📊 لوحة التحكم</h1>
                <p>نظرة شاملة على إحصائيات النظام والبيانات</p>
            </div>
            
            <div class="card-body">
                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger">
                    <h4>❌ خطأ في تحميل البيانات</h4>
                    <p><?php echo $error_message; ?></p>
                </div>
                <?php else: ?>
                
                <!-- مؤشرات الأداء الرئيسية -->
                <h3>📈 مؤشرات الأداء الرئيسية (KPIs)</h3>
                <div class="kpi-grid">
                    <div class="kpi-card">
                        <div class="kpi-number"><?php echo number_format($stats['accounting']['total'] ?? 0); ?></div>
                        <div class="kpi-label">طلبات المحاسبة</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-number"><?php echo number_format($stats['conflict']['total'] ?? 0); ?></div>
                        <div class="kpi-label">طلبات إنهاء النزاع</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-number"><?php echo number_format($stats['dispute']['total'] ?? 0); ?></div>
                        <div class="kpi-label">طلبات تسوية النزاع</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-number"><?php echo number_format(($stats['accounting']['total'] ?? 0) + ($stats['conflict']['total'] ?? 0) + ($stats['dispute']['total'] ?? 0)); ?></div>
                        <div class="kpi-label">إجمالي الطلبات</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-number"><?php echo number_format($classification_count); ?></div>
                        <div class="kpi-label">المأموريات</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-number"><?php echo number_format($container_count); ?></div>
                        <div class="kpi-label">أنواع الوعاء</div>
                    </div>
                </div>
                
                <!-- المخططات البيانية -->
                <h3>📊 المخططات البيانية التفاعلية</h3>
                <div class="dashboard-grid">
                    <!-- مخطط توزيع الطلبات -->
                    <div class="chart-container">
                        <div class="chart-title">توزيع الطلبات حسب النوع</div>
                        <canvas id="requestsDistributionChart"></canvas>
                    </div>
                    
                    <!-- مخطط الطلبات الشهرية -->
                    <div class="chart-container">
                        <div class="chart-title">الطلبات خلال آخر 12 شهر</div>
                        <canvas id="monthlyRequestsChart"></canvas>
                    </div>
                    
                    <!-- مخطط المناطق -->
                    <div class="chart-container">
                        <div class="chart-title">توزيع المأموريات حسب المنطقة</div>
                        <canvas id="regionsChart"></canvas>
                    </div>
                    
                    <!-- مخطط مديري العموم -->
                    <div class="chart-container">
                        <div class="chart-title">توزيع المأموريات حسب مديري العموم</div>
                        <canvas id="managersChart"></canvas>
                    </div>
                </div>
                
                <!-- ملخص سريع -->
                <div class="summary-section">
                    <h3>📋 ملخص سريع</h3>
                    <div class="row">
                        <div class="col-4">
                            <h4>أكثر المناطق نشاطاً</h4>
                            <?php foreach (array_slice($regions_stats, 0, 5) as $region): ?>
                            <p><?php echo htmlspecialchars($region['region']); ?>: <?php echo $region['count']; ?> مأمورية</p>
                            <?php endforeach; ?>
                        </div>
                        <div class="col-4">
                            <h4>مديري العموم</h4>
                            <?php foreach (array_slice($managers_stats, 0, 5) as $manager): ?>
                            <p><?php echo htmlspecialchars($manager['general_manager']); ?>: <?php echo $manager['count']; ?> مأمورية</p>
                            <?php endforeach; ?>
                        </div>
                        <div class="col-4">
                            <h4>روابط سريعة</h4>
                            <p><a href="reports/accounting.php">تقارير المحاسبة</a></p>
                            <p><a href="reports/conflict.php">تقارير إنهاء النزاع</a></p>
                            <p><a href="reports/dispute.php">تقارير تسوية النزاع</a></p>
                            <p><a href="search.php">البحث المتقدم</a></p>
                        </div>
                    </div>
                </div>
                
                <?php endif; ?>
                
                <div style="text-align: center; margin-top: 30px;">
                    <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                    <a href="import_data.php" class="btn btn-success">استيراد البيانات</a>
                    <a href="search.php" class="btn btn-info">البحث المتقدم</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات المخططات من PHP
        const chartData = {
            requests: {
                accounting: <?php echo $stats['accounting']['total'] ?? 0; ?>,
                conflict: <?php echo $stats['conflict']['total'] ?? 0; ?>,
                dispute: <?php echo $stats['dispute']['total'] ?? 0; ?>
            },
            regions: <?php echo json_encode($regions_stats); ?>,
            managers: <?php echo json_encode($managers_stats); ?>,
            monthly: <?php echo json_encode($stats['accounting']['by_month'] ?? []); ?>
        };
    </script>
    <script src="assets/js/dashboard.js"></script>
</body>
</html>
