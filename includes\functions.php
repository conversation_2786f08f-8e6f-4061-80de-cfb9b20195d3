<?php
/**
 * الدوال المساعدة للنظام
 * TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @date 2025-06-17
 */

require_once dirname(__DIR__) . '/config/database.php';

/**
 * دالة لاستخراج التاريخ من اسم الملف
 */
function extractDateFromFilename($filename) {
    if (preg_match('/EXPORT_(\d{8})/', $filename, $matches)) {
        $dateString = $matches[1];
        $year = substr($dateString, 0, 4);
        $month = substr($dateString, 4, 2);
        $day = substr($dateString, 6, 2);
        return "$year-$month-$day";
    }
    return date('Y-m-d');
}

/**
 * دالة لتنظيف البيانات المستوردة
 */
function cleanImportData($data) {
    $data = str_replace(',', '', $data);
    $data = trim($data);
    return $data;
}

/**
 * دالة لتحويل التاريخ من تنسيق DD.MM.YYYY إلى YYYY-MM-DD
 */
function convertDateTime($dateTime) {
    if (empty($dateTime)) return null;
    
    if (preg_match('/(\d{2})\.(\d{2})\.(\d{4})\s+(\d{2}:\d{2}:\d{2})/', $dateTime, $matches)) {
        return $matches[3] . '-' . $matches[2] . '-' . $matches[1] . ' ' . $matches[4];
    }
    
    return $dateTime;
}

/**
 * دالة للحصول على إحصائيات الجدول
 */
function getTableStats($tableName) {
    try {
        $pdo = getDBConnection();
        
        $stats = [];
        
        // إجمالي السجلات
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM `$tableName`");
        $stats['total'] = $stmt->fetchColumn();
        
        // السجلات حسب الحالة
        $stmt = $pdo->query("
            SELECT request_status, COUNT(*) as count 
            FROM `$tableName` 
            GROUP BY request_status 
            ORDER BY count DESC
        ");
        $stats['by_status'] = $stmt->fetchAll();
        
        // السجلات حسب المأمورية (أكثر 10)
        $stmt = $pdo->query("
            SELECT office_code, office_name, COUNT(*) as count 
            FROM `$tableName` 
            GROUP BY office_code, office_name 
            ORDER BY count DESC 
            LIMIT 10
        ");
        $stats['by_office'] = $stmt->fetchAll();
        
        // السجلات حسب تاريخ التصدير
        $stmt = $pdo->query("
            SELECT export_date, COUNT(*) as count 
            FROM `$tableName` 
            GROUP BY export_date 
            ORDER BY export_date DESC 
            LIMIT 12
        ");
        $stats['by_export_date'] = $stmt->fetchAll();
        
        return $stats;
        
    } catch (Exception $e) {
        error_log("Error getting table stats: " . $e->getMessage());
        return [];
    }
}

/**
 * دالة للحصول على بيانات الرسم البياني
 */
function getChartData($tableName, $type = 'status') {
    try {
        $pdo = getDBConnection();
        
        switch ($type) {
            case 'status':
                $stmt = $pdo->query("
                    SELECT 
                        rs.name as label,
                        COUNT(t.id) as value,
                        rs.code
                    FROM `$tableName` t
                    LEFT JOIN request_status rs ON t.request_status = rs.code
                    GROUP BY t.request_status, rs.name, rs.code
                    ORDER BY value DESC
                ");
                break;
                
            case 'container':
                $stmt = $pdo->query("
                    SELECT 
                        c.name as label,
                        COUNT(t.id) as value,
                        c.code
                    FROM `$tableName` t
                    LEFT JOIN container c ON t.container = c.code
                    GROUP BY t.container, c.name, c.code
                    ORDER BY value DESC
                ");
                break;
                
            case 'monthly':
                $stmt = $pdo->query("
                    SELECT 
                        DATE_FORMAT(export_date, '%Y-%m') as label,
                        COUNT(*) as value
                    FROM `$tableName`
                    GROUP BY DATE_FORMAT(export_date, '%Y-%m')
                    ORDER BY label DESC
                    LIMIT 12
                ");
                break;
                
            default:
                return [];
        }
        
        return $stmt->fetchAll();
        
    } catch (Exception $e) {
        error_log("Error getting chart data: " . $e->getMessage());
        return [];
    }
}

/**
 * دالة للبحث في الجدول
 */
function searchTable($tableName, $filters = [], $page = 1, $limit = 50) {
    try {
        $pdo = getDBConnection();
        
        $where = [];
        $params = [];
        
        // بناء شروط البحث
        if (!empty($filters['request_number'])) {
            $where[] = "request_number LIKE ?";
            $params[] = '%' . $filters['request_number'] . '%';
        }
        
        if (!empty($filters['taxpayer_name'])) {
            $where[] = "taxpayer_name LIKE ?";
            $params[] = '%' . $filters['taxpayer_name'] . '%';
        }
        
        if (!empty($filters['office_code'])) {
            $where[] = "office_code = ?";
            $params[] = $filters['office_code'];
        }
        
        if (!empty($filters['request_status'])) {
            $where[] = "request_status = ?";
            $params[] = $filters['request_status'];
        }
        
        if (!empty($filters['container'])) {
            $where[] = "container = ?";
            $params[] = $filters['container'];
        }
        
        if (!empty($filters['export_date_from'])) {
            $where[] = "export_date >= ?";
            $params[] = $filters['export_date_from'];
        }
        
        if (!empty($filters['export_date_to'])) {
            $where[] = "export_date <= ?";
            $params[] = $filters['export_date_to'];
        }
        
        $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
        
        // حساب إجمالي السجلات
        $countSql = "SELECT COUNT(*) FROM `$tableName` $whereClause";
        $stmt = $pdo->prepare($countSql);
        $stmt->execute($params);
        $totalRecords = $stmt->fetchColumn();
        
        // حساب الصفحات
        $totalPages = ceil($totalRecords / $limit);
        $offset = ($page - 1) * $limit;
        
        // جلب البيانات
        $dataSql = "
            SELECT * FROM `$tableName` 
            $whereClause 
            ORDER BY created_at DESC 
            LIMIT $limit OFFSET $offset
        ";
        $stmt = $pdo->prepare($dataSql);
        $stmt->execute($params);
        $records = $stmt->fetchAll();
        
        return [
            'records' => $records,
            'total_records' => $totalRecords,
            'total_pages' => $totalPages,
            'current_page' => $page,
            'limit' => $limit
        ];
        
    } catch (Exception $e) {
        error_log("Error searching table: " . $e->getMessage());
        return [
            'records' => [],
            'total_records' => 0,
            'total_pages' => 0,
            'current_page' => 1,
            'limit' => $limit
        ];
    }
}

/**
 * دالة لتصدير البيانات إلى CSV
 */
function exportToCSV($tableName, $filters = []) {
    try {
        $pdo = getDBConnection();
        
        $where = [];
        $params = [];
        
        // بناء شروط البحث (نفس منطق البحث)
        if (!empty($filters['office_code'])) {
            $where[] = "office_code = ?";
            $params[] = $filters['office_code'];
        }
        
        if (!empty($filters['request_status'])) {
            $where[] = "request_status = ?";
            $params[] = $filters['request_status'];
        }
        
        if (!empty($filters['export_date_from'])) {
            $where[] = "export_date >= ?";
            $params[] = $filters['export_date_from'];
        }
        
        if (!empty($filters['export_date_to'])) {
            $where[] = "export_date <= ?";
            $params[] = $filters['export_date_to'];
        }
        
        $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
        
        $sql = "SELECT * FROM `$tableName` $whereClause ORDER BY created_at DESC";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        $filename = $tableName . '_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // إضافة BOM للدعم العربي في Excel
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // كتابة العناوين
        $firstRow = $stmt->fetch();
        if ($firstRow) {
            fputcsv($output, array_keys($firstRow));
            fputcsv($output, $firstRow);
            
            while ($row = $stmt->fetch()) {
                fputcsv($output, $row);
            }
        }
        
        fclose($output);
        exit;
        
    } catch (Exception $e) {
        error_log("Error exporting to CSV: " . $e->getMessage());
        return false;
    }
}

/**
 * دالة للحصول على قائمة المأموريات من جدول التصنيف
 */
function getOfficesList($tableName = 'accounting') {
    try {
        $pdo = getDBConnection();

        // محاولة الحصول على البيانات من جدول التصنيف أولاً
        $stmt = $pdo->query("
            SELECT DISTINCT office_code, office_name
            FROM classification
            ORDER BY office_name
        ");
        $result = $stmt->fetchAll();

        // إذا لم توجد بيانات في جدول التصنيف، استخدم الجدول الأساسي
        if (empty($result)) {
            $stmt = $pdo->query("
                SELECT DISTINCT office_code, office_name
                FROM `$tableName`
                WHERE office_code IS NOT NULL AND office_name IS NOT NULL
                ORDER BY office_name
            ");
            $result = $stmt->fetchAll();
        }

        return $result;
    } catch (Exception $e) {
        return [];
    }
}

/**
 * دالة للحصول على قائمة أنواع الوعاء من جدول الوعاء
 */
function getContainersList() {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->query("
            SELECT container_code, container_name, classification
            FROM container
            ORDER BY container_code
        ");
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

/**
 * دالة للحصول على قائمة حالات الطلب من جدول حالة الطلب
 */
function getRequestStatusList() {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->query("
            SELECT status_code, status_description
            FROM request_status
            ORDER BY status_code
        ");
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

/**
 * دالة لإنشاء رسالة تنبيه
 */
function createAlert($message, $type = 'info') {
    $alertClass = [
        'success' => 'alert-success',
        'error' => 'alert-error',
        'warning' => 'alert-warning',
        'info' => 'alert-info'
    ];
    
    $class = isset($alertClass[$type]) ? $alertClass[$type] : 'alert-info';
    
    return "<div class='alert $class'>$message</div>";
}

/**
 * دالة للتحقق من صحة تنسيق التاريخ
 */
function validateDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

/**
 * دالة لحساب النسبة المئوية
 */
function calculatePercentage($part, $total) {
    if ($total == 0) return 0;
    return round(($part / $total) * 100, 2);
}

/**
 * دالة لاستخراج تاريخ التصدير من اسم الملف
 * تنسيق اسم الملف: TableNameEXPORT_YYYYMMDD.csv
 */
function extractExportDate($filename) {
    // البحث عن نمط EXPORT_YYYYMMDD في اسم الملف
    if (preg_match('/EXPORT_(\d{8})/', $filename, $matches)) {
        $dateString = $matches[1];
        // تحويل YYYYMMDD إلى تنسيق Y-m-d
        $year = substr($dateString, 0, 4);
        $month = substr($dateString, 4, 2);
        $day = substr($dateString, 6, 2);

        // التحقق من صحة التاريخ
        if (checkdate($month, $day, $year)) {
            return "$year-$month-$day";
        }
    }

    // إذا لم يتم العثور على التاريخ أو كان غير صحيح، استخدم التاريخ الحالي
    return date('Y-m-d');
}

/**
 * دالة لاستخراج نوع الجدول من اسم الملف
 */
function extractTableType($filename) {
    $filename = strtolower($filename);

    if (strpos($filename, 'accounting') !== false) {
        return 'accounting';
    } elseif (strpos($filename, 'conflict') !== false) {
        return 'conflict';
    } elseif (strpos($filename, 'dispute') !== false) {
        return 'dispute';
    }

    return null;
}

/**
 * دالة محسنة لاستيراد البيانات بدون مفتاح أساسي
 */
function importCSVDataWithoutId($tableName, $csvFile, $clearTable = false) {
    try {
        $pdo = getDBConnection();

        // استخراج تاريخ التصدير من اسم الملف
        $exportDate = extractExportDate($csvFile['name']);

        // حذف البيانات حسب الخيار المحدد
        if ($clearTable) {
            // حذف جميع البيانات
            $pdo->exec("DELETE FROM `$tableName`");
            echo "<p>✅ تم حذف جميع البيانات السابقة من جدول $tableName</p>";
        } else {
            // حذف بيانات نفس تاريخ التصدير فقط
            $stmt = $pdo->prepare("DELETE FROM `$tableName` WHERE export_date = ?");
            $stmt->execute([$exportDate]);
            $deletedRows = $stmt->rowCount();
            echo "<p>✅ تم حذف $deletedRows سجل من تاريخ $exportDate في جدول $tableName</p>";
        }

        // قراءة ملف CSV
        $handle = fopen($csvFile['tmp_name'], 'r');
        if (!$handle) {
            throw new Exception("لا يمكن فتح ملف CSV");
        }

        // قراءة العنوان الأول (أسماء الأعمدة)
        $headers = fgetcsv($handle);
        if (!$headers) {
            throw new Exception("ملف CSV فارغ أو تالف");
        }

        // تنظيف أسماء الأعمدة
        $headers = array_map('trim', $headers);

        // إضافة export_date إلى الأعمدة
        $headers[] = 'export_date';

        // بناء استعلام الإدراج
        $placeholders = str_repeat('?,', count($headers) - 1) . '?';
        $columns = '`' . implode('`, `', $headers) . '`';
        $sql = "INSERT INTO `$tableName` ($columns) VALUES ($placeholders)";
        $stmt = $pdo->prepare($sql);

        $rowCount = 0;
        $errorCount = 0;

        // قراءة البيانات وإدراجها
        while (($data = fgetcsv($handle)) !== FALSE) {
            try {
                // تنظيف البيانات
                $data = array_map('trim', $data);

                // إضافة تاريخ التصدير
                $data[] = $exportDate;

                // التأكد من تطابق عدد البيانات مع عدد الأعمدة
                if (count($data) === count($headers)) {
                    $stmt->execute($data);
                    $rowCount++;
                } else {
                    $errorCount++;
                }
            } catch (Exception $e) {
                $errorCount++;
                // تسجيل الخطأ دون إيقاف العملية
                error_log("خطأ في إدراج السجل: " . $e->getMessage());
            }
        }

        fclose($handle);

        echo "<p>✅ تم استيراد $rowCount سجل بنجاح إلى جدول $tableName</p>";
        if ($errorCount > 0) {
            echo "<p>⚠️ تم تجاهل $errorCount سجل بسبب أخطاء في البيانات</p>";
        }
        echo "<p>📅 تاريخ التصدير: $exportDate</p>";

        return true;

    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في استيراد البيانات: " . $e->getMessage() . "</p>";
        return false;
    }
}
?>
