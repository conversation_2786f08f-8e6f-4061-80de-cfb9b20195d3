<?php
/**
 * تحليل هيكل ملفات CSV - TIaF Report System
 * تحديد أسماء الحقول الصحيحة لكل جدول
 * 
 * <AUTHOR> Development Team
 * @version 1.4.3
 * @date 2025-06-17
 */

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحليل هيكل ملفات CSV - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: '<PERSON><PERSON>wal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 0.9rem;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تحليل هيكل ملفات CSV</h1>
            <p>تحديد أسماء الحقول الصحيحة لإنشاء الجداول</p>
        </div>
        
        <div class="content">
            <?php
            // تحليل الملفات المرفوعة
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_files'])) {
                $files = $_FILES['csv_files'];
                
                echo "<div class='section'>";
                echo "<h3>📊 نتائج التحليل</h3>";
                
                for ($i = 0; $i < count($files['name']); $i++) {
                    if ($files['error'][$i] === UPLOAD_ERR_OK) {
                        $fileName = $files['name'][$i];
                        $tmpName = $files['tmp_name'][$i];
                        
                        echo "<h4>📁 ملف: " . htmlspecialchars($fileName) . "</h4>";
                        
                        // تحديد نوع الجدول من اسم الملف
                        $tableType = 'unknown';
                        if (strpos($fileName, 'Accounting') !== false) {
                            $tableType = 'accounting';
                        } elseif (strpos($fileName, 'Conflict') !== false) {
                            $tableType = 'conflict';
                        } elseif (strpos($fileName, 'Dispute') !== false) {
                            $tableType = 'dispute';
                        }
                        
                        // قراءة العناوين
                        $handle = fopen($tmpName, 'r');
                        $headers = fgetcsv($handle);
                        
                        // تنظيف العناوين
                        $cleanHeaders = array_map(function($header) {
                            return trim(str_replace("\xEF\xBB\xBF", '', $header));
                        }, $headers);
                        
                        echo "<p><strong>نوع الجدول:</strong> $tableType</p>";
                        echo "<p><strong>عدد الأعمدة:</strong> " . count($cleanHeaders) . "</p>";
                        
                        // عرض العناوين في جدول
                        echo "<table>";
                        echo "<tr><th>الرقم</th><th>اسم العمود (العربي)</th><th>اسم الحقل المقترح (الإنجليزي)</th><th>نوع البيانات المقترح</th></tr>";
                        
                        foreach ($cleanHeaders as $index => $header) {
                            $englishName = suggestEnglishFieldName($header);
                            $dataType = suggestDataType($header);
                            
                            echo "<tr>";
                            echo "<td>" . ($index + 1) . "</td>";
                            echo "<td>" . htmlspecialchars($header) . "</td>";
                            echo "<td>" . htmlspecialchars($englishName) . "</td>";
                            echo "<td>" . htmlspecialchars($dataType) . "</td>";
                            echo "</tr>";
                        }
                        
                        echo "</table>";
                        
                        // إنشاء SQL للجدول
                        echo "<h5>📝 SQL المقترح لإنشاء الجدول:</h5>";
                        echo "<pre>";
                        echo generateCreateTableSQL($tableType, $cleanHeaders);
                        echo "</pre>";
                        
                        fclose($handle);
                        echo "<hr>";
                    }
                }
                
                echo "</div>";
            }
            
            // دالة لاقتراح اسم الحقل الإنجليزي
            function suggestEnglishFieldName($arabicName) {
                $mapping = [
                    'رقم الطلب' => 'request_number',
                    'تاريخ الإنشاء' => 'creation_date',
                    'منشئ الطلب' => 'created_by',
                    'تاريخ التعديل' => 'modification_date',
                    'معدل الطلب' => 'modified_by',
                    'حالة الطلب' => 'request_status',
                    'وصف الحالة' => 'status_description',
                    'نوع الطلب' => 'request_type',
                    'اسم نوع الطلب' => 'request_type_name',
                    'رقم التسجيل الضريبي' => 'tax_registration_number',
                    'كود المأمورية' => 'office_code',
                    'اسم المأمورية' => 'office_name',
                    'اسم المكلف' => 'taxpayer_name',
                    'العنوان' => 'address',
                    'رقم الهاتف' => 'phone_number',
                    'البريد الإلكتروني' => 'email',
                    'نوع المحاسبة' => 'accounting_type',
                    'وصف نوع المحاسبة' => 'accounting_type_description',
                    'الوعاء' => 'container',
                    'اسم الوعاء' => 'container_name',
                    'الفترة' => 'period',
                    'مرحلة النزاع' => 'dispute_stage',
                    'وصف مرحلة النزاع' => 'dispute_stage_description',
                    'رقم القضية' => 'case_number',
                    'جهة النزاع' => 'dispute_authority',
                    'اسم جهة النزاع' => 'dispute_authority_name',
                    'جهة أخرى' => 'other_authority',
                    'الضريبة طبقاً للإقرار' => 'tax_according_to_declaration',
                    'الضريبة من آخر ربط' => 'tax_from_last_assessment',
                    'سنة آخر ربط' => 'last_assessment_year',
                    'الضريبة طبقاً للنموذج' => 'tax_according_to_form',
                    'الضريبة المسددة' => 'tax_paid',
                    'الضريبة المتوقعة' => 'expected_tax',
                    'الضريبة طبقاً للقانون' => 'tax_according_to_law',
                    'الضريبة المستحقة' => 'tax_due_payment'
                ];
                
                if (isset($mapping[$arabicName])) {
                    return $mapping[$arabicName];
                }
                
                // إنشاء اسم تلقائي
                $englishName = strtolower($arabicName);
                $englishName = str_replace([' ', '-', '/', '\\', '(', ')', '[', ']'], '_', $englishName);
                $englishName = preg_replace('/[^a-z0-9_]/', '', $englishName);
                $englishName = 'field_' . substr(md5($arabicName), 0, 8);
                
                return $englishName;
            }
            
            // دالة لاقتراح نوع البيانات
            function suggestDataType($fieldName) {
                if (strpos($fieldName, 'تاريخ') !== false || strpos($fieldName, 'Date') !== false) {
                    return 'datetime';
                } elseif (strpos($fieldName, 'ضريبة') !== false || strpos($fieldName, 'Tax') !== false || strpos($fieldName, 'مبلغ') !== false) {
                    return 'decimal(15,2)';
                } elseif (strpos($fieldName, 'رقم') !== false && strpos($fieldName, 'هاتف') === false) {
                    return 'varchar(50)';
                } elseif (strpos($fieldName, 'كود') !== false || strpos($fieldName, 'Code') !== false) {
                    return 'varchar(10)';
                } elseif (strpos($fieldName, 'عنوان') !== false || strpos($fieldName, 'Address') !== false) {
                    return 'text';
                } elseif (strpos($fieldName, 'وصف') !== false || strpos($fieldName, 'Description') !== false) {
                    return 'varchar(255)';
                } else {
                    return 'varchar(255)';
                }
            }
            
            // دالة لإنشاء SQL
            function generateCreateTableSQL($tableName, $headers) {
                $sql = "CREATE TABLE `$tableName` (\n";
                
                foreach ($headers as $header) {
                    $fieldName = suggestEnglishFieldName($header);
                    $dataType = suggestDataType($header);
                    $sql .= "    `$fieldName` $dataType COMMENT '" . addslashes($header) . " | " . ucwords(str_replace('_', ' ', $fieldName)) . "',\n";
                }
                
                $sql .= "    `export_date` date NOT NULL COMMENT 'تاريخ التصدير | Export Date',\n";
                $sql .= "    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,\n";
                $sql .= "    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n";
                $sql .= "    \n";
                $sql .= "    KEY `idx_export_date` (`export_date`),\n";
                $sql .= "    KEY `idx_request_number` (`request_number`)\n";
                $sql .= ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci\n";
                $sql .= "COMMENT='جدول $tableName - " . count($headers) . " حقل أساسي + export_date';";
                
                return $sql;
            }
            ?>
            
            <!-- نموذج رفع الملفات -->
            <div class="section">
                <h3>📤 رفع ملفات CSV للتحليل</h3>
                <p>ارفع ملفات AccountingEXPORT، ConflictEXPORT، و DisputeEXPORT لتحليل هيكلها:</p>
                
                <form method="post" enctype="multipart/form-data">
                    <div class="upload-area">
                        <p>📁 اختر ملفات CSV (يمكن اختيار عدة ملفات)</p>
                        <input type="file" name="csv_files[]" accept=".csv" multiple required>
                        <p><small>اختر ملفات: AccountingEXPORT_*.csv, ConflictEXPORT_*.csv, DisputeEXPORT_*.csv</small></p>
                    </div>
                    <button type="submit" class="btn">🔍 تحليل الملفات</button>
                </form>
            </div>
            
            <!-- معلومات مفيدة -->
            <div class="section warning">
                <h3>💡 معلومات مفيدة</h3>
                <h4>الهدف من هذه الأداة:</h4>
                <ul>
                    <li>تحديد العدد الصحيح للحقول في كل جدول</li>
                    <li>إنشاء أسماء حقول إنجليزية مناسبة</li>
                    <li>اقتراح أنواع البيانات المناسبة</li>
                    <li>إنشاء SQL جاهز لإنشاء الجداول</li>
                </ul>
                
                <h4>المتطلبات الحالية:</h4>
                <ul>
                    <li><strong>جدول accounting:</strong> 23 حقل + export_date</li>
                    <li><strong>جدول conflict:</strong> 39 حقل + export_date</li>
                    <li><strong>جدول dispute:</strong> 39 حقل + export_date</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="recreate_main_tables_correct.php" class="btn">إنشاء الجداول</a>
                <a href="debug_import.php" class="btn">تشخيص الاستيراد</a>
            </div>
        </div>
    </div>
</body>
</html>
