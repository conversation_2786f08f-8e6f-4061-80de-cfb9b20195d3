<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استيراد البيانات - نظام تقارير منظومة التيسيرات الضريبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-primary">نظام التقارير الضريبية</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="database_setup.php">
                                <i class="fas fa-database me-2"></i>إعداد قاعدة البيانات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="import.php">
                                <i class="fas fa-upload me-2"></i>استيراد البيانات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar me-2"></i>التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="fas fa-upload me-2"></i>استيراد البيانات</h1>
                </div>

                <?php
                // Include required files
                require_once 'config/database.php';

                // Create includes directory if it doesn't exist
                if (!is_dir('includes')) {
                    mkdir('includes', 0755, true);
                }

                // Include import functions
                require_once 'includes/import_functions.php';

                // Handle form submission
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    $action = $_POST['action'] ?? '';

                    try {
                        switch ($action) {
                            case 'import_accounting':
                                $result = importAccountingData($_FILES['csv_file']);
                                echo '<div class="alert alert-success"><i class="fas fa-check me-2"></i>' . $result['message'] . '</div>';
                                break;

                            case 'import_conflict':
                                $result = importConflictData($_FILES['csv_file']);
                                echo '<div class="alert alert-success"><i class="fas fa-check me-2"></i>' . $result['message'] . '</div>';
                                break;

                            case 'import_dispute':
                                $result = importDisputeData($_FILES['csv_file']);
                                echo '<div class="alert alert-success"><i class="fas fa-check me-2"></i>' . $result['message'] . '</div>';
                                break;
                        }
                    } catch (Exception $e) {
                        echo '<div class="alert alert-danger"><i class="fas fa-times me-2"></i>خطأ: ' . $e->getMessage() . '</div>';
                    }
                }

                // Get import statistics
                $stats = getImportStatistics();
                ?>

                <!-- Import Statistics -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-calculator fa-3x text-primary mb-3"></i>
                                <h5>طلبات المحاسبة</h5>
                                <h3 class="text-primary"><?php echo number_format($stats['accounting']); ?></h3>
                                <small class="text-muted">سجل مستورد</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-balance-scale fa-3x text-warning mb-3"></i>
                                <h5>طلبات إنهاء النزاع</h5>
                                <h3 class="text-warning"><?php echo number_format($stats['conflict']); ?></h3>
                                <small class="text-muted">سجل مستورد</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-gavel fa-3x text-success mb-3"></i>
                                <h5>طلبات تسوية النزاع</h5>
                                <h3 class="text-success"><?php echo number_format($stats['dispute']); ?></h3>
                                <small class="text-muted">سجل مستورد</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Import Forms -->
                <div class="row">
                    <!-- Accounting Import -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>استيراد طلبات المحاسبة</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" enctype="multipart/form-data" id="accountingForm">
                                    <input type="hidden" name="action" value="import_accounting">

                                    <div class="mb-3">
                                        <label class="form-label">ملف CSV</label>
                                        <div class="file-upload-area" onclick="document.getElementById('accountingFile').click()">
                                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                            <p>انقر لاختيار ملف أو اسحب الملف هنا</p>
                                            <small class="text-muted">AccountingEXPORT_YYYYMMDD.csv</small>
                                        </div>
                                        <input type="file" id="accountingFile" name="csv_file" accept=".csv" style="display: none;" required>
                                    </div>

                                    <div class="file-info"></div>

                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-upload me-2"></i>استيراد البيانات
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Conflict Import -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-balance-scale me-2"></i>استيراد طلبات إنهاء النزاع</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" enctype="multipart/form-data" id="conflictForm">
                                    <input type="hidden" name="action" value="import_conflict">

                                    <div class="mb-3">
                                        <label class="form-label">ملف CSV</label>
                                        <div class="file-upload-area" onclick="document.getElementById('conflictFile').click()">
                                            <i class="fas fa-cloud-upload-alt fa-3x text-warning mb-3"></i>
                                            <p>انقر لاختيار ملف أو اسحب الملف هنا</p>
                                            <small class="text-muted">ConflictEXPORT_YYYYMMDD.csv</small>
                                        </div>
                                        <input type="file" id="conflictFile" name="csv_file" accept=".csv" style="display: none;" required>
                                    </div>

                                    <div class="file-info"></div>

                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-upload me-2"></i>استيراد البيانات
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Dispute Import -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-gavel me-2"></i>استيراد طلبات تسوية النزاع</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" enctype="multipart/form-data" id="disputeForm">
                                    <input type="hidden" name="action" value="import_dispute">

                                    <div class="mb-3">
                                        <label class="form-label">ملف CSV</label>
                                        <div class="file-upload-area" onclick="document.getElementById('disputeFile').click()">
                                            <i class="fas fa-cloud-upload-alt fa-3x text-success mb-3"></i>
                                            <p>انقر لاختيار ملف أو اسحب الملف هنا</p>
                                            <small class="text-muted">DisputeEXPORT_YYYYMMDD.csv</small>
                                        </div>
                                        <input type="file" id="disputeFile" name="csv_file" accept=".csv" style="display: none;" required>
                                    </div>

                                    <div class="file-info"></div>

                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-upload me-2"></i>استيراد البيانات
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Import Instructions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>تعليمات الاستيراد</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-primary">متطلبات الملفات:</h6>
                                        <ul>
                                            <li>يجب أن تكون الملفات بصيغة CSV</li>
                                            <li>يجب أن تحتوي على ترميز UTF-8</li>
                                            <li>يجب أن تتبع تنسيق الأسماء المحدد</li>
                                            <li>يجب أن تحتوي على جميع الأعمدة المطلوبة</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-success">تنسيق أسماء الملفات:</h6>
                                        <ul>
                                            <li><code>AccountingEXPORT_20250526.csv</code></li>
                                            <li><code>ConflictEXPORT_20250526.csv</code></li>
                                            <li><code>DisputeEXPORT_20250526.csv</code></li>
                                        </ul>
                                        <small class="text-muted">سيتم استخراج تاريخ التصدير من اسم الملف تلقائياً</small>
                                    </div>
                                </div>

                                <div class="alert alert-warning mt-3">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>ملاحظات مهمة:</h6>
                                    <ul class="mb-0">
                                        <li>تأكد من إعداد قاعدة البيانات قبل الاستيراد</li>
                                        <li>يمكن استيراد نفس الملف عدة مرات (لا توجد قيود فرادة)</li>
                                        <li>سيتم إضافة تاريخ التصدير تلقائياً من اسم الملف</li>
                                        <li>تأكد من وجود ملفات Mapping Table قبل الاستيراد</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Imports -->
                <?php $recentImports = getRecentImports(); ?>
                <?php if (!empty($recentImports)): ?>
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-history me-2"></i>عمليات الاستيراد الأخيرة</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>نوع البيانات</th>
                                                <th>اسم الملف</th>
                                                <th>تاريخ التصدير</th>
                                                <th>عدد السجلات</th>
                                                <th>تاريخ الاستيراد</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentImports as $import): ?>
                                            <tr>
                                                <td>
                                                    <?php
                                                    $icons = [
                                                        'accounting' => '<i class="fas fa-calculator text-primary"></i> طلبات المحاسبة',
                                                        'conflict' => '<i class="fas fa-balance-scale text-warning"></i> طلبات إنهاء النزاع',
                                                        'dispute' => '<i class="fas fa-gavel text-success"></i> طلبات تسوية النزاع'
                                                    ];
                                                    echo $icons[$import['type']] ?? $import['type'];
                                                    ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($import['filename']); ?></td>
                                                <td><?php echo $import['export_date']; ?></td>
                                                <td><?php echo number_format($import['records_count']); ?></td>
                                                <td><?php echo $import['import_date']; ?></td>
                                                <td>
                                                    <span class="badge bg-success">نجح</span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // تخصيص معالجة رفع الملفات لكل نموذج
        document.addEventListener('DOMContentLoaded', function() {
            // معالجة نموذج المحاسبة
            const accountingFile = document.getElementById('accountingFile');
            accountingFile.addEventListener('change', function() {
                validateAccountingFile(this);
            });

            // معالجة نموذج النزاعات
            const conflictFile = document.getElementById('conflictFile');
            conflictFile.addEventListener('change', function() {
                validateConflictFile(this);
            });

            // معالجة نموذج المنازعات
            const disputeFile = document.getElementById('disputeFile');
            disputeFile.addEventListener('change', function() {
                validateDisputeFile(this);
            });
        });

        function validateAccountingFile(input) {
            const file = input.files[0];
            if (file) {
                if (!file.name.toLowerCase().includes('accounting')) {
                    TaxReportSystem.showWarning('تأكد من أن اسم الملف يحتوي على "Accounting"');
                }
                displayFileInfo(file, input.closest('.card').querySelector('.file-info'));
            }
        }

        function validateConflictFile(input) {
            const file = input.files[0];
            if (file) {
                if (!file.name.toLowerCase().includes('conflict')) {
                    TaxReportSystem.showWarning('تأكد من أن اسم الملف يحتوي على "Conflict"');
                }
                displayFileInfo(file, input.closest('.card').querySelector('.file-info'));
            }
        }

        function validateDisputeFile(input) {
            const file = input.files[0];
            if (file) {
                if (!file.name.toLowerCase().includes('dispute')) {
                    TaxReportSystem.showWarning('تأكد من أن اسم الملف يحتوي على "Dispute"');
                }
                displayFileInfo(file, input.closest('.card').querySelector('.file-info'));
            }
        }

        function displayFileInfo(file, container) {
            const exportDate = TaxReportSystem.extractDateFromFilename(file.name);
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-file me-2"></i>
                    <strong>الملف:</strong> ${file.name}<br>
                    <strong>الحجم:</strong> ${formatFileSize(file.size)}<br>
                    ${exportDate ? `<strong>تاريخ التصدير:</strong> ${exportDate}<br>` : ''}
                    <strong>النوع:</strong> ${file.type || 'CSV'}
                </div>
            `;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 بايت';
            const k = 1024;
            const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
