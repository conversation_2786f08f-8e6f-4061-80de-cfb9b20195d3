<?php
/**
 * فحص هيكل الجداول - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔍 فحص هيكل الجداول</h2>";
    
    $tables = ['accounting', 'conflict', 'dispute'];
    
    foreach ($tables as $table) {
        echo "<h3>📊 جدول $table</h3>";
        
        try {
            // فحص هيكل الجدول
            $stmt = $pdo->query("DESCRIBE `$table`");
            $columns = $stmt->fetchAll();
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>اسم الحقل</th><th>نوع البيانات</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
            echo "</tr>";
            
            $has_created_at = false;
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>" . $column['Field'] . "</td>";
                echo "<td>" . $column['Type'] . "</td>";
                echo "<td>" . $column['Null'] . "</td>";
                echo "<td>" . $column['Key'] . "</td>";
                echo "<td>" . $column['Default'] . "</td>";
                echo "<td>" . $column['Extra'] . "</td>";
                echo "</tr>";
                
                if ($column['Field'] === 'created_at') {
                    $has_created_at = true;
                }
            }
            echo "</table>";
            
            echo "<p><strong>عدد الحقول:</strong> " . count($columns) . "</p>";
            echo "<p><strong>يحتوي على created_at:</strong> " . ($has_created_at ? "✅ نعم" : "❌ لا") . "</p>";
            
            // عدد السجلات
            $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
            $count = $stmt->fetchColumn();
            echo "<p><strong>عدد السجلات:</strong> " . number_format($count) . "</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في جدول $table: " . $e->getMessage() . "</p>";
        }
        
        echo "<hr>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</p>";
}
?>
