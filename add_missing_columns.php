<?php
/**
 * إضافة الأعمدة المفقودة للجداول الموجودة - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.4.6
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

// الأعمدة المطلوب إضافتها
$missingColumns = [
    'status_creation_date' => 'datetime COMMENT "تاريخ انشاء الحالة | Status Creation Date"',
    'tax_according_to_declaration_before_modification' => 'decimal(15,2) COMMENT "الضريبة طبقاً للإقرار قبل التعديل | Tax According To Declaration Before Modification"',
    'tax_from_last_assessment_before_modification' => 'decimal(15,2) COMMENT "الضريبة من آخر ربط قبل التعديل | Tax From Last Assessment Before Modification"',
    'tax_according_to_form_before_modification' => 'decimal(15,2) COMMENT "الضريبة طبقاً للنموذج قبل التعديل | Tax According To Form Before Modification"',
    'tax_paid_before_modification' => 'decimal(15,2) COMMENT "الضريبة المسددة قبل التعديل | Tax Paid Before Modification"'
];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة الأعمدة المفقودة - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            cursor: pointer;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 0.9rem;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>➕ إضافة الأعمدة المفقودة</h1>
            <p>إضافة الأعمدة المفقودة للجداول الموجودة بدون فقدان البيانات</p>
        </div>
        
        <div class="content">
            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_columns'])) {
                try {
                    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    echo "<div class='section success'>";
                    echo "<h3>✅ تم الاتصال بقاعدة البيانات بنجاح</h3>";
                    echo "</div>";
                    
                    $tables = ['accounting', 'conflict', 'dispute'];
                    $totalAdded = 0;
                    
                    foreach ($tables as $tableName) {
                        echo "<div class='section'>";
                        echo "<h3>🔄 معالجة جدول $tableName</h3>";
                        
                        // الحصول على الأعمدة الموجودة
                        $stmt = $pdo->query("DESCRIBE `$tableName`");
                        $existingColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        
                        $addedToTable = 0;
                        
                        foreach ($missingColumns as $columnName => $columnDefinition) {
                            if (!in_array($columnName, $existingColumns)) {
                                try {
                                    // إضافة العمود
                                    $sql = "ALTER TABLE `$tableName` ADD COLUMN `$columnName` $columnDefinition";
                                    $pdo->exec($sql);
                                    echo "<p>✅ تم إضافة العمود: $columnName</p>";
                                    $addedToTable++;
                                    $totalAdded++;
                                } catch (PDOException $e) {
                                    echo "<p>❌ خطأ في إضافة العمود $columnName: " . $e->getMessage() . "</p>";
                                }
                            } else {
                                echo "<p>ℹ️ العمود $columnName موجود بالفعل</p>";
                            }
                        }
                        
                        if ($addedToTable === 0) {
                            echo "<p>✅ جميع الأعمدة موجودة في الجدول</p>";
                        }
                        
                        // عرض عدد الأعمدة الحالي
                        $stmt = $pdo->query("DESCRIBE `$tableName`");
                        $currentColumns = $stmt->fetchAll();
                        echo "<p>📊 إجمالي الأعمدة الحالية: " . count($currentColumns) . "</p>";
                        
                        echo "</div>";
                    }
                    
                    // ملخص النتائج
                    echo "<div class='section success'>";
                    echo "<h3>🎉 تم الانتهاء من إضافة الأعمدة!</h3>";
                    echo "<p><strong>إجمالي الأعمدة المضافة:</strong> $totalAdded</p>";
                    echo "<p>يمكنك الآن استيراد البيانات بنجاح.</p>";
                    echo "</div>";
                    
                } catch (PDOException $e) {
                    echo "<div class='section error'>";
                    echo "<h3>❌ خطأ في قاعدة البيانات</h3>";
                    echo "<p>" . $e->getMessage() . "</p>";
                    echo "</div>";
                }
            }
            ?>
            
            <!-- معلومات الأعمدة المراد إضافتها -->
            <div class="section">
                <h3>📋 الأعمدة المراد إضافتها</h3>
                <table>
                    <tr><th>اسم العمود</th><th>نوع البيانات</th><th>التعليق</th></tr>
                    <?php foreach ($missingColumns as $columnName => $definition): ?>
                    <tr>
                        <td><?php echo $columnName; ?></td>
                        <td><?php echo explode(' COMMENT', $definition)[0]; ?></td>
                        <td><?php 
                            preg_match('/COMMENT "([^"]*)"/', $definition, $matches);
                            echo isset($matches[1]) ? $matches[1] : '';
                        ?></td>
                    </tr>
                    <?php endforeach; ?>
                </table>
            </div>
            
            <!-- فحص الجداول الحالية -->
            <div class="section">
                <h3>🔍 فحص الجداول الحالية</h3>
                <?php
                try {
                    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    $tables = ['accounting', 'conflict', 'dispute'];
                    
                    foreach ($tables as $tableName) {
                        echo "<h4>جدول $tableName:</h4>";
                        
                        try {
                            $stmt = $pdo->query("DESCRIBE `$tableName`");
                            $existingColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                            
                            echo "<p>📊 عدد الأعمدة الحالية: " . count($existingColumns) . "</p>";
                            
                            $missingInTable = [];
                            foreach ($missingColumns as $columnName => $definition) {
                                if (!in_array($columnName, $existingColumns)) {
                                    $missingInTable[] = $columnName;
                                }
                            }
                            
                            if (empty($missingInTable)) {
                                echo "<p>✅ جميع الأعمدة المطلوبة موجودة</p>";
                            } else {
                                echo "<p>❌ الأعمدة المفقودة: " . implode(', ', $missingInTable) . "</p>";
                            }
                            
                        } catch (PDOException $e) {
                            echo "<p>❌ خطأ في فحص الجدول: " . $e->getMessage() . "</p>";
                        }
                    }
                    
                } catch (PDOException $e) {
                    echo "<p>❌ خطأ في الاتصال: " . $e->getMessage() . "</p>";
                }
                ?>
            </div>
            
            <!-- تحذير -->
            <div class="section warning">
                <h3>⚠️ معلومات مهمة</h3>
                <p>هذه العملية ستقوم بـ:</p>
                <ul>
                    <li><strong>إضافة الأعمدة المفقودة فقط</strong> للجداول الموجودة</li>
                    <li><strong>الحفاظ على جميع البيانات الموجودة</strong></li>
                    <li><strong>عدم تعديل الأعمدة الموجودة</strong></li>
                </ul>
                <p>هذه العملية آمنة ولن تؤثر على البيانات الموجودة.</p>
            </div>
            
            <!-- نموذج الإضافة -->
            <div class="section">
                <h3>➕ إضافة الأعمدة المفقودة</h3>
                <p>اضغط الزر أدناه لإضافة الأعمدة المفقودة للجداول:</p>
                
                <form method="post">
                    <input type="hidden" name="add_columns" value="1">
                    <button type="submit" class="btn btn-success">
                        ➕ إضافة الأعمدة المفقودة
                    </button>
                </form>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="import_data.php" class="btn">استيراد البيانات</a>
                <a href="recreate_tables_with_mapping.php" class="btn">إعادة إنشاء الجداول</a>
            </div>
        </div>
    </div>
</body>
</html>
