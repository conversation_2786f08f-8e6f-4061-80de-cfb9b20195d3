<?php
/**
 * تحميل الملفات المقسمة - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.4.6
 * @date 2025-06-17
 */

// التحقق من وجود معامل الملف
if (!isset($_GET['file']) || empty($_GET['file'])) {
    die('اسم الملف غير محدد');
}

$fileName = basename($_GET['file']);
$filePath = 'temp/split_files/' . $fileName;

// التحقق من وجود الملف
if (!file_exists($filePath)) {
    die('الملف غير موجود');
}

// التحقق من أن الملف من النوع المسموح
$allowedExtensions = ['csv'];
$fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

if (!in_array($fileExtension, $allowedExtensions)) {
    die('نوع الملف غير مسموح');
}

// التحقق من أن الملف داخل المجلد المسموح
$realPath = realpath($filePath);
$allowedPath = realpath('temp/split_files/');

if (strpos($realPath, $allowedPath) !== 0) {
    die('مسار الملف غير صحيح');
}

// إعداد headers للتحميل
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="' . $fileName . '"');
header('Content-Length: ' . filesize($filePath));
header('Cache-Control: must-revalidate');
header('Pragma: public');

// قراءة وإرسال الملف
readfile($filePath);

// حذف الملف بعد التحميل (اختياري)
// unlink($filePath);

exit;
?>
