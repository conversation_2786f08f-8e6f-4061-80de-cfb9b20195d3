<?php
/**
 * إضافة عمود created_at للجداول الثلاثة - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔧 إضافة عمود created_at للجداول الثلاثة</h2>";
    
    $tables = ['accounting', 'conflict', 'dispute'];
    
    foreach ($tables as $table) {
        echo "<h3>📊 معالجة جدول $table</h3>";
        
        try {
            // فحص وجود العمود أولاً
            $stmt = $pdo->query("SHOW COLUMNS FROM `$table` LIKE 'created_at'");
            $column_exists = $stmt->rowCount() > 0;
            
            if ($column_exists) {
                echo "<p>✅ العمود created_at موجود بالفعل في جدول $table</p>";
            } else {
                // إضافة العمود
                $sql = "ALTER TABLE `$table` ADD COLUMN `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإدراج في النظام'";
                $pdo->exec($sql);
                echo "<p>✅ تم إضافة العمود created_at إلى جدول $table</p>";
            }
            
            // فحص عدد السجلات
            $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
            $count = $stmt->fetchColumn();
            echo "<p>📊 عدد السجلات في الجدول: " . number_format($count) . "</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في جدول $table: " . $e->getMessage() . "</p>";
        }
        
        echo "<hr>";
    }
    
    echo "<h3>🎉 تم الانتهاء من المعالجة</h3>";
    echo "<p>الآن يمكن استخدام ORDER BY a.created_at في جميع الاستعلامات</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</p>";
}
?>

<div style="margin-top: 30px; text-align: center;">
    <a href="import_data.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">العودة لصفحة الاستيراد</a>
    <a href="check_table_structure.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">فحص هيكل الجداول</a>
</div>
