<?php
/**
 * صفحة استيراد البيانات من ملفات CSV
 * TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

// دالة لاستخراج التاريخ من اسم الملف
function extractDateFromFilename($filename) {
    // البحث عن نمط التاريخ في اسم الملف (مثل: EXPORT_20250526)
    if (preg_match('/EXPORT_(\d{8})/', $filename, $matches)) {
        $dateString = $matches[1];
        // تحويل من YYYYMMDD إلى YYYY-MM-DD
        $year = substr($dateString, 0, 4);
        $month = substr($dateString, 4, 2);
        $day = substr($dateString, 6, 2);
        return "$year-$month-$day";
    }
    return date('Y-m-d'); // التاريخ الحالي كافتراضي
}

// دالة لتنظيف البيانات
function cleanData($data) {
    // إزالة الفواصل من الأرقام
    $data = str_replace(',', '', $data);
    // إزالة المسافات الزائدة
    $data = trim($data);
    return $data;
}

// دالة لتحويل التاريخ من تنسيق DD.MM.YYYY HH:MM:SS إلى YYYY-MM-DD HH:MM:SS
function convertDateTime($dateTime) {
    if (empty($dateTime)) return null;

    // تحويل التاريخ من DD.MM.YYYY HH:MM:SS إلى YYYY-MM-DD HH:MM:SS
    if (preg_match('/(\d{2})\.(\d{2})\.(\d{4})\s+(\d{2}:\d{2}:\d{2})/', $dateTime, $matches)) {
        return $matches[3] . '-' . $matches[2] . '-' . $matches[1] . ' ' . $matches[4];
    }

    return $dateTime;
}

// دالة لتحويل حقول التاريخ بناءً على نوع الجدول
function convertDateTimeFields($table, $headers, $data) {
    $dateTimeFields = [];

    // تحديد حقول التاريخ لكل جدول
    switch ($table) {
        case 'accounting':
            $dateTimeFields = ['status_creation_date', 'modification_date'];
            break;
        case 'conflict':
        case 'dispute':
            $dateTimeFields = ['status_creation_date', 'modification_date'];
            break;
    }

    // تحويل حقول التاريخ
    for ($i = 0; $i < count($headers) && $i < count($data); $i++) {
        if (in_array($headers[$i], $dateTimeFields)) {
            $data[$i] = convertDateTime($data[$i]);
        }
    }

    return $data;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استيراد البيانات - TIaF Report System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Tajawal', 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 40px;
        }
        
        .upload-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px dashed #dee2e6;
        }
        
        .upload-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .file-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            margin-bottom: 15px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .table-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .table-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #667eea;
        }
        
        .table-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .table-card p {
            color: #6c757d;
            line-height: 1.6;
        }

        #batch-info {
            background: #e3f2fd;
            color: #1565c0;
            padding: 8px 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
            text-align: center;
        }

        .processing-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }

        .processing-info h5 {
            color: #856404;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗂️ استيراد البيانات</h1>
            <p>نظام تقارير الضرائب والرسوم</p>
        </div>
        
        <div class="content">
            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['import_table'])) {
                $table = $_POST['import_table'];
                $uploadedFile = $_FILES['csv_file'];
                $clearTable = isset($_POST['clear_table']) && $_POST['clear_table'] === '1';
                
                if ($uploadedFile['error'] === UPLOAD_ERR_OK) {
                    try {
                        // زيادة مهلة التنفيذ للملفات الكبيرة
                        set_time_limit(0); // إزالة حد الوقت
                        ini_set('memory_limit', '512M'); // زيادة الذاكرة

                        $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
                        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                        // التأكد من عدم وجود معاملة نشطة من البداية
                        if ($pdo->inTransaction()) {
                            $pdo->rollBack();
                        }

                        // تحسين الأداء - بدون بدء معاملة تلقائية
                        $pdo->exec("SET unique_checks = 0");
                        $pdo->exec("SET foreign_key_checks = 0");
                        
                        $filename = $uploadedFile['name'];
                        $exportDate = extractDateFromFilename($filename);
                        
                        echo "<div class='alert alert-info'>";
                        echo "<h4>🚀 بدء استيراد البيانات...</h4>";
                        echo "<p><strong>الملف:</strong> $filename</p>";
                        echo "<p><strong>الجدول:</strong> $table</p>";
                        echo "<p><strong>تاريخ التصدير:</strong> $exportDate (مستخرج من اسم الملف)</p>";
                        echo "<p><strong>تنسيق التاريخ:</strong> " . date('d-m-Y', strtotime($exportDate)) . "</p>";
                        echo "</div>";
                        
                        // قراءة ملف CSV
                        $handle = fopen($uploadedFile['tmp_name'], 'r');
                        if ($handle !== FALSE) {
                            // قراءة العنوان الأول (أسماء الأعمدة)
                            $headers = fgetcsv($handle);

                            // تنظيف أسماء الأعمدة وإزالة BOM
                            $cleanHeaders = array_map(function($header) {
                                // إزالة BOM (Byte Order Mark)
                                $header = str_replace("\xEF\xBB\xBF", '', $header);
                                // إزالة المسافات والرموز الخفية
                                $header = trim($header);
                                return $header;
                            }, $headers);

                            // تحويل أسماء الأعمدة العربية إلى أسماء الحقول الإنجليزية
                            $columnMapping = getColumnMapping();
                            $mappedHeaders = [];

                            foreach ($cleanHeaders as $header) {
                                if (isset($columnMapping[$header])) {
                                    $mappedHeaders[] = $columnMapping[$header];
                                } else {
                                    // إذا لم يتم العثور على تطابق، استخدم الاسم كما هو
                                    $mappedHeaders[] = $header;
                                }
                            }

                            echo "<p>📋 تم العثور على " . count($mappedHeaders) . " عمود في الملف</p>";
                            echo "<p>🔄 أسماء الأعمدة الأولى: " . implode(', ', array_slice($mappedHeaders, 0, 5)) . "...</p>";
                            echo "<p>📊 الجدول المستهدف: <strong>$table</strong></p>";

                            // تصحيح أسماء الحقول حسب الجدول المحدد
                            $correctedHeaders = correctHeadersForTable($table, $mappedHeaders);

                            // إزالة التكرارات من أسماء الأعمدة
                            $uniqueHeaders = removeDuplicateColumns($correctedHeaders);

                            // عرض معلومات إضافية حسب الجدول
                            $expectedColumns = getExpectedColumnsCount($table);
                            echo "<p>📈 عدد الأعمدة المتوقع: $expectedColumns (+ export_date)</p>";
                            echo "<p>🔧 عدد الأعمدة بعد إزالة التكرارات: " . count($uniqueHeaders) . "</p>";

                            if (count($mappedHeaders) !== $expectedColumns) {
                                echo "<p style='color: orange;'>⚠️ تحذير: عدد الأعمدة لا يطابق المتوقع. قد تحدث أخطاء أثناء الاستيراد.</p>";
                            }

                            if (count($mappedHeaders) !== count($uniqueHeaders)) {
                                echo "<p style='color: blue;'>ℹ️ تم إزالة " . (count($mappedHeaders) - count($uniqueHeaders)) . " عمود مكرر</p>";
                            }
                            
                            $rowCount = 0;
                            $successCount = 0;
                            $errorCount = 0;
                            $batchSize = 1000; // معالجة 1000 سجل في كل دفعة
                            $batchCount = 0;

                            echo "<div class='progress'><div class='progress-bar' style='width: 0%'></div></div>";
                            echo "<div id='progress-text'>جاري المعالجة...</div>";
                            echo "<div id='batch-info'>معالجة بالدفعات (1000 سجل لكل دفعة)</div>";
                            
                            // حذف البيانات حسب الخيار المحدد (بدون معاملة)
                            if ($clearTable) {
                                // حذف جميع البيانات من الجدول
                                $deleteSql = "DELETE FROM `$table`";
                                $deleteStmt = $pdo->prepare($deleteSql);
                                $deleteStmt->execute();
                                echo "<p>🗑️ تم حذف جميع البيانات من الجدول</p>";
                            } else {
                                // حذف البيانات السابقة لنفس تاريخ التصدير فقط
                                $deleteSql = "DELETE FROM `$table` WHERE export_date = ?";
                                $deleteStmt = $pdo->prepare($deleteSql);
                                $deleteStmt->execute([$exportDate]);
                                echo "<p>🗑️ تم حذف البيانات السابقة لتاريخ: $exportDate</p>";
                            }

                            // إعداد الاستعلام حسب الجدول
                            $sql = prepareInsertSQL($table, $uniqueHeaders);
                            $stmt = $pdo->prepare($sql);

                            echo "<p>🔧 استعلام SQL: " . substr($sql, 0, 100) . "...</p>";

                            // التأكد من عدم وجود معاملة نشطة قبل البدء
                            if ($pdo->inTransaction()) {
                                $pdo->rollBack();
                                echo "<p style='color: orange;'>⚠️ تم إلغاء معاملة سابقة</p>";
                            }

                            echo "<p style='color: green;'>✅ جاهز للاستيراد بمعاملات صغيرة (دفعة = معاملة)</p>";

                            $batchData = [];

                            while (($data = fgetcsv($handle)) !== FALSE) {
                                $rowCount++;

                                try {
                                    // تنظيف البيانات
                                    $cleanData = array_map('cleanData', $data);

                                    // التأكد من أن عدد البيانات يطابق عدد الأعمدة المتوقع
                                    $expectedDataCount = count($uniqueHeaders);
                                    if (count($cleanData) > $expectedDataCount) {
                                        // قطع البيانات الزائدة
                                        $cleanData = array_slice($cleanData, 0, $expectedDataCount);
                                    } elseif (count($cleanData) < $expectedDataCount) {
                                        // إضافة قيم فارغة للبيانات المفقودة
                                        while (count($cleanData) < $expectedDataCount) {
                                            $cleanData[] = null;
                                        }
                                    }

                                    // إضافة تاريخ التصدير
                                    $cleanData[] = $exportDate;

                                    // تحويل التواريخ بناءً على نوع الجدول
                                    $cleanData = convertDateTimeFields($table, $uniqueHeaders, $cleanData);

                                    // إضافة البيانات للدفعة
                                    $batchData[] = $cleanData;

                                    // معالجة الدفعة عند الوصول للحد المطلوب
                                    if (count($batchData) >= $batchSize) {
                                        processBatch($stmt, $batchData, $successCount, $errorCount, $batchCount);
                                        $batchData = [];
                                        $batchCount++;

                                        // عرض تحديث كل 10 دفعات
                                        if ($batchCount % 10 === 0) {
                                            echo "<script>
                                                document.getElementById('batch-info').textContent = 'تم معالجة " . ($batchCount * $batchSize) . " سجل في " . $batchCount . " دفعة...';
                                            </script>";
                                            flush();
                                        }
                                    }

                                } catch (Exception $e) {
                                    $errorCount++;
                                    if ($errorCount <= 10) { // عرض أول 10 أخطاء فقط
                                        echo "<p style='color: red;'>❌ خطأ في السطر $rowCount: " . $e->getMessage() . "</p>";
                                    }
                                }

                                // تحديث شريط التقدم كل 1000 سجل
                                if ($rowCount % 1000 === 0) {
                                    $progress = min(100, ($rowCount / 100000) * 100); // افتراض 100k سجل كحد أقصى
                                    echo "<script>
                                        document.querySelector('.progress-bar').style.width = '$progress%';
                                        document.getElementById('progress-text').textContent = 'تم معالجة " . number_format($rowCount) . " سجل...';
                                    </script>";
                                    flush();
                                }
                            }

                            // معالجة الدفعة الأخيرة
                            if (!empty($batchData)) {
                                processBatch($stmt, $batchData, $successCount, $errorCount, $batchCount + 1);
                            }

                            // التأكد من عدم وجود معاملة معلقة
                            if ($pdo->inTransaction()) {
                                $pdo->commit();
                                echo "<p style='color: green;'>✅ تم إنهاء آخر معاملة</p>";
                            }

                            // إعادة تفعيل الإعدادات
                            $pdo->exec("SET unique_checks = 1");
                            $pdo->exec("SET foreign_key_checks = 1");

                            echo "<p style='color: green;'>✅ تم استعادة إعدادات قاعدة البيانات</p>";
                            
                            fclose($handle);
                            
                            $endTime = microtime(true);
                            $executionTime = round($endTime - ($_SERVER['REQUEST_TIME_FLOAT'] ?? $endTime), 2);

                            echo "<div class='alert alert-success'>";
                            echo "<h4>✅ تم الانتهاء من الاستيراد بنجاح!</h4>";
                            echo "<p><strong>📊 إحصائيات الاستيراد:</strong></p>";
                            echo "<ul>";
                            echo "<li>📁 <strong>الملف:</strong> $filename</li>";
                            echo "<li>🗂️ <strong>الجدول:</strong> $table</li>";
                            echo "<li>📅 <strong>تاريخ التصدير:</strong> $exportDate (" . date('d-m-Y', strtotime($exportDate)) . ")</li>";
                            echo "<li>📋 <strong>إجمالي السجلات:</strong> " . number_format($rowCount) . "</li>";
                            echo "<li>✅ <strong>تم بنجاح:</strong> " . number_format($successCount) . "</li>";
                            echo "<li>❌ <strong>أخطاء:</strong> " . number_format($errorCount) . "</li>";
                            echo "<li>📈 <strong>معدل النجاح:</strong> " . round(($successCount / max($rowCount, 1)) * 100, 2) . "%</li>";
                            echo "<li>⏱️ <strong>وقت التنفيذ:</strong> $executionTime ثانية</li>";
                            echo "<li>🚀 <strong>معدل المعالجة:</strong> " . number_format($rowCount / max($executionTime, 1)) . " سجل/ثانية</li>";
                            echo "</ul>";
                            if ($errorCount > 0) {
                                echo "<p style='color: orange;'>⚠️ <strong>تنبيه:</strong> تم تسجيل " . number_format($errorCount) . " خطأ أثناء الاستيراد.</p>";
                                if ($errorCount > 10) {
                                    echo "<p style='color: orange;'>📝 تم عرض أول 10 أخطاء فقط. باقي الأخطاء مسجلة في ملف السجل.</p>";
                                }
                            }
                            echo "</div>";
                            
                        } else {
                            echo "<div class='alert alert-error'>❌ خطأ في قراءة الملف</div>";
                        }

                    } catch (Exception $e) {
                        // التراجع عن المعاملة في حالة الخطأ
                        if (isset($pdo) && $pdo->inTransaction()) {
                            $pdo->rollBack();
                        }

                        // إعادة تفعيل الإعدادات في حالة الخطأ
                        if (isset($pdo)) {
                            try {
                                $pdo->exec("SET unique_checks = 1");
                                $pdo->exec("SET foreign_key_checks = 1");
                            } catch (Exception $resetError) {
                                // تجاهل أخطاء إعادة التعيين
                            }
                        }

                        echo "<div class='alert alert-error'>❌ خطأ: " . $e->getMessage() . "</div>";
                        echo "<div class='alert alert-error'>📝 تم التراجع عن جميع التغييرات</div>";
                    }
                } else {
                    echo "<div class='alert alert-error'>❌ خطأ في رفع الملف</div>";
                }
            }

            // دالة لمعالجة دفعة من البيانات مع معاملة منفصلة
            function processBatch($stmt, $batchData, &$successCount, &$errorCount, $batchNumber) {
                global $pdo;
                $batchErrors = 0;

                // بدء معاملة صغيرة لهذه الدفعة فقط
                try {
                    if ($pdo->inTransaction()) {
                        $pdo->commit(); // إنهاء أي معاملة سابقة
                    }
                    $pdo->beginTransaction();

                    foreach ($batchData as $index => $data) {
                        try {
                            $stmt->execute($data);
                            $successCount++;
                        } catch (Exception $e) {
                            $errorCount++;
                            $batchErrors++;

                            // عرض أول خطأ في كل دفعة فقط لتوفير الوقت
                            if ($batchErrors === 1 && $errorCount <= 20) {
                                echo "<p style='color: red; font-size: 0.9em;'>❌ خطأ في الدفعة $batchNumber: " . $e->getMessage() . "</p>";
                            }

                            // تسجيل جميع الأخطاء في ملف السجل
                            error_log("Batch $batchNumber, Row " . ($index + 1) . " Error: " . $e->getMessage());
                        }
                    }

                    // تأكيد المعاملة للدفعة
                    $pdo->commit();

                } catch (Exception $batchError) {
                    // في حالة خطأ في المعاملة، التراجع والمتابعة
                    if ($pdo->inTransaction()) {
                        $pdo->rollBack();
                    }
                    echo "<p style='color: red;'>❌ خطأ في معاملة الدفعة $batchNumber: " . $batchError->getMessage() . "</p>";
                }

                // عرض ملخص الدفعة إذا كان هناك أخطاء
                if ($batchErrors > 1 && $errorCount <= 20) {
                    echo "<p style='color: orange; font-size: 0.9em;'>⚠️ الدفعة $batchNumber: $batchErrors خطأ إضافي (مسجل في ملف السجل)</p>";
                }
            }

            // دالة لإعداد استعلام الإدراج
            function prepareInsertSQL($table, $headers) {
                $columns = implode('`, `', $headers);
                $placeholders = str_repeat('?,', count($headers)) . '?'; // إضافة placeholder لتاريخ التصدير
                $placeholders = rtrim($placeholders, ',');

                return "INSERT INTO `$table` (`$columns`, `export_date`) VALUES ($placeholders)";
            }

            // دالة لإرجاع عدد الأعمدة المتوقع لكل جدول
            function getExpectedColumnsCount($table) {
                switch ($table) {
                    case 'accounting':
                        return 23; // 23 حقل + export_date
                    case 'conflict':
                    case 'dispute':
                        return 39; // 39 حقل + export_date
                    default:
                        return 0;
                }
            }

            // دالة لإزالة التكرارات من أسماء الأعمدة
            function removeDuplicateColumns($headers) {
                $uniqueHeaders = [];
                $seen = [];

                foreach ($headers as $header) {
                    if (!isset($seen[$header])) {
                        $uniqueHeaders[] = $header;
                        $seen[$header] = true;
                    }
                }

                return $uniqueHeaders;
            }

            // دالة لتصحيح أسماء الحقول حسب الجدول المحدد
            function correctHeadersForTable($table, $headers) {
                // معالجة خاصة لجدول المنازعة (Dispute) حيث يختلف ترتيب الحقلين الأخيرين
                if ($table === 'dispute') {
                    $correctedHeaders = [];
                    for ($i = 0; $i < count($headers); $i++) {
                        $header = $headers[$i];

                        // في جدول المنازعة، الحقلان الأخيران مختلطان في ملف CSV:
                        // الحقل 38: "الضريبة طبقا للقانون" → يجب أن يكون expected_tax
                        // الحقل 39: "الضريبه المتوقعة" → يجب أن يكون tax_according_to_law
                        if ($i === count($headers) - 2) { // الحقل قبل الأخير
                            if ($header === 'tax_according_to_law') {
                                $correctedHeaders[] = 'expected_tax'; // تصحيح الترتيب
                            } else {
                                $correctedHeaders[] = $header;
                            }
                        } elseif ($i === count($headers) - 1) { // الحقل الأخير
                            if ($header === 'expected_tax') {
                                $correctedHeaders[] = 'tax_according_to_law'; // تصحيح الترتيب
                            } else {
                                $correctedHeaders[] = $header;
                            }
                        } else {
                            $correctedHeaders[] = $header;
                        }
                    }
                    return $correctedHeaders;
                }

                return $headers; // للجداول الأخرى، إرجاع الحقول كما هي
            }

            // دالة لتحويل أسماء الأعمدة العربية إلى أسماء الحقول الإنجليزية
            // بناءً على ملفات CSV الفعلية الجديدة - تطابق تام مع هيكل قاعدة البيانات المحدث
            function getColumnMapping() {
                return [
                    // جدول المحاسبة (Accounting) - 23 حقل بناءً على AccountingEXPORT_20250526.csv
                    'رقم الطلب' => 'request_number',
                    'تاريخ انشاء الحالة' => 'status_creation_date',
                    'منشأ بواسطة' => 'created_by',
                    'تم التعديل بواسطة' => 'modified_by',
                    'تاريخ التعديل' => 'modification_date',
                    'الحالة' => 'request_status',
                    'وصف الحالة' => 'status_description',
                    'رقم التسجيل الضريبي' => 'tax_registration_number',
                    'كود المأمورية' => 'office_code',
                    'اسم المأمورية' => 'office_name',
                    'اسم الممول' => 'taxpayer_name',
                    'العنوان' => 'address',
                    'البريد الالكتروني' => 'email',
                    'رقم التليفون' => 'phone_number',
                    'الوعاء' => 'container',
                    'اسم الوعاء' => 'container_name',
                    'الفترة' => 'period',
                    'قيمه الوعاء' => 'container_cost',
                    'تكلفة اقتناء الاسهم' => 'acquiring_cost',
                    'قيمة الوعاء قبل التعديل' => 'container_cost_before',
                    'تكلفة اقتناء الاسهم قبل التعديل' => 'acquiring_cost_before',
                    'الضريبه المتوقعة' => 'expected_tax',
                    'الضريبة طبقا للقانون' => 'tax_according_to_law',

                    // جداول النزاع والمنازعة (Conflict & Dispute) - 39 حقل
                    'تاريخ انشاء الطلب' => 'status_creation_date',
                    'حالة الطلب' => 'request_status',
                    'وصف حالة الطلب' => 'status_description',
                    'وصف الحالة' => 'status_description', // للتوافق مع جدول المنازعة
                    'نوع الطلب' => 'request_type',
                    'اسم نوع الطلب' => 'request_type_name',
                    'نوع المحاسبة' => 'accounting_type',
                    'وصف نوع المحاسبة' => 'accounting_type_description',
                    'مرحله النزاع' => 'dispute_stage',
                    'وصف مرحلة النزاع' => 'dispute_stage_description',
                    'اسم مرحلة النزاع' => 'dispute_stage_description', // للتوافق مع جدول المنازعة
                    'رقم الدعوي / الطعن' => 'case_number',
                    'جهة نظر النزاع' => 'dispute_authority',
                    'اسم جهة نظر النزاع' => 'dispute_authority_name',
                    'جهة نظر أخرى' => 'other_authority',
                    'الضريبة طبقا للاقرار' => 'tax_according_to_declaration',
                    'الضريبة من اخر ربط' => 'tax_from_last_assessment',
                    'سنه آخر ربط' => 'last_assessment_year',
                    'الضريبة طبقا للنموذج' => 'tax_according_to_form',
                    'الضريبه المسددة' => 'tax_paid',
                    'الضريبة طبقا للاقرار قبل التعديل' => 'tax_according_to_declaration_before_modification',
                    'الضريبة من اخر ربط قبل التعديل' => 'tax_from_last_assessment_before_modification',
                    'الضريبة طبقا للنموذج قبل التعديل' => 'tax_according_to_form_before_modification',
                    'الضريبه المسددة قبل التعديل' => 'tax_paid_before_modification',
                    'الضريبه المستحقه السداد' => 'tax_due_payment',

                    // حقل خاص بجدول المنازعة (Dispute) - يختلف عن النزاع
                    'وصف الوعاء' => 'container_name',

                    // حقل التاريخ المشترك (لا يتم استيراده من CSV - يتم إضافته تلقائياً)
                    'تاريخ الانشاء' => 'export_date'
                ];
            }
            ?>
            
            <div class="table-info">
                <div class="table-card">
                    <h4>📊 طلبات المحاسبة (Accounting)</h4>
                    <p><strong>23 حقل + export_date</strong> - استيراد بيانات طلبات المحاسبة من ملفات CSV</p>
                    <p><small>تنسيق الملف: <code>AccountingEXPORT_YYYYMMDD.csv</code></small></p>
                    <p><small>مثال: AccountingEXPORT_20250526.csv → تاريخ التصدير: 26-05-2025</small></p>
                    <form method="post" enctype="multipart/form-data" style="margin-top: 15px;">
                        <input type="hidden" name="import_table" value="accounting">
                        <input type="file" name="csv_file" accept=".csv" class="file-input" required>
                        <div style="margin: 10px 0;">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="clear_table" value="1">
                                <span>حذف جميع البيانات السابقة من الجدول قبل الاستيراد</span>
                            </label>
                        </div>
                        <button type="submit" class="btn btn-success">استيراد بيانات المحاسبة</button>
                    </form>
                </div>

                <div class="table-card">
                    <h4>⚖️ طلبات إنهاء النزاع (Conflict)</h4>
                    <p><strong>39 حقل + export_date</strong> - استيراد بيانات طلبات إنهاء النزاع من ملفات CSV</p>
                    <p><small>تنسيق الملف: <code>ConflictEXPORT_YYYYMMDD.csv</code></small></p>
                    <p><small>مثال: ConflictEXPORT_20250526.csv → تاريخ التصدير: 26-05-2025</small></p>
                    <form method="post" enctype="multipart/form-data" style="margin-top: 15px;">
                        <input type="hidden" name="import_table" value="conflict">
                        <input type="file" name="csv_file" accept=".csv" class="file-input" required>
                        <div style="margin: 10px 0;">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="clear_table" value="1">
                                <span>حذف جميع البيانات السابقة من الجدول قبل الاستيراد</span>
                            </label>
                        </div>
                        <button type="submit" class="btn btn-success">استيراد بيانات إنهاء النزاع</button>
                    </form>
                </div>

                <div class="table-card">
                    <h4>🤝 طلبات تسوية النزاع (Dispute)</h4>
                    <p><strong>39 حقل + export_date</strong> - استيراد بيانات طلبات تسوية النزاع من ملفات CSV</p>
                    <p><small>تنسيق الملف: <code>DisputeEXPORT_YYYYMMDD.csv</code></small></p>
                    <p><small>مثال: DisputeEXPORT_20250526.csv → تاريخ التصدير: 26-05-2025</small></p>
                    <form method="post" enctype="multipart/form-data" style="margin-top: 15px;">
                        <input type="hidden" name="import_table" value="dispute">
                        <input type="file" name="csv_file" accept=".csv" class="file-input" required>
                        <div style="margin: 10px 0;">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="clear_table" value="1">
                                <span>حذف جميع البيانات السابقة من الجدول قبل الاستيراد</span>
                            </label>
                        </div>
                        <button type="submit" class="btn btn-success">استيراد بيانات تسوية النزاع</button>
                    </form>
                </div>
            </div>

            <div style="background: #f8f9fa; border-radius: 10px; padding: 20px; margin-top: 30px;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">📋 ملاحظات مهمة حول الاستيراد</h4>
                <ul style="color: #6c757d; line-height: 1.8;">
                    <li><strong>تاريخ التصدير:</strong> يتم استخراجه تلقائياً من اسم الملف (EXPORT_YYYYMMDD)</li>
                    <li><strong>تنسيق التاريخ:</strong> يتم تحويله من YYYYMMDD إلى YYYY-MM-DD في قاعدة البيانات</li>
                    <li><strong>حذف البيانات:</strong> بدون تحديد الخيار، يتم حذف البيانات لنفس تاريخ التصدير فقط</li>
                    <li><strong>الحقول:</strong> تطابق تماماً مع ملفات CSV المرجعية</li>
                    <li><strong>الترميز:</strong> يدعم الملفات بترميز UTF-8 مع BOM</li>
                </ul>
            </div>

            <div style="background: #e8f5e8; border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">🚀 تحسينات الأداء للملفات الكبيرة</h4>
                <ul style="color: #6c757d; line-height: 1.8;">
                    <li><strong>معالجة بالدفعات:</strong> 1000 سجل في كل دفعة لتحسين الأداء</li>
                    <li><strong>إزالة حد الوقت:</strong> لا يوجد حد زمني للتنفيذ</li>
                    <li><strong>زيادة الذاكرة:</strong> حتى 512MB للملفات الكبيرة</li>
                    <li><strong>تحسين قاعدة البيانات:</strong> إيقاف الفحوصات أثناء الاستيراد</li>
                    <li><strong>معاملات محسنة:</strong> تأكيد كل 10,000 سجل</li>
                    <li><strong>تسجيل الأخطاء:</strong> الأخطاء الزائدة تُسجل في ملف السجل</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin-top: 40px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="dashboard.php" class="btn btn-success">عرض لوحة التحكم</a>
            </div>
        </div>
    </div>
</body>
</html>
