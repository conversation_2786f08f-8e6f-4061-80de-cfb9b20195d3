# دليل البدء السريع - TIaF Report System

## نظرة عامة

هذا الدليل يوضح كيفية إعداد واستخدام نظام TIaF Report System بشكل صحيح لضمان استيراد ناجح للبيانات.

## الخطوات الأساسية

### 1. إعداد قاعدة البيانات

#### أ. إنشاء قاعدة البيانات:
```sql
CREATE DATABASE tiaf_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### ب. إنشاء الجداول المرجعية:
```
http://localhost/TIaF-Report/database_setup.php
```

#### ج. إنشاء الجداول الأساسية بالتحويل الصحيح:
```
http://localhost/TIaF-Report/recreate_tables_with_mapping.php
← اضغط "إعادة إنشاء الجداول"
```

### 2. استيراد البيانات

#### أ. تحضير ملفات CSV:
- تأكد من تنسيق اسم الملف: `TableNameEXPORT_YYYYMMDD.csv`
- أمثلة:
  - `AccountingEXPORT_20250526.csv`
  - `ConflictEXPORT_20250526.csv`
  - `DisputeEXPORT_20250526.csv`

#### ب. استيراد البيانات:
```
http://localhost/TIaF-Report/import_data.php
← اختر الجدول المناسب
← ارفع ملف CSV
← اضغط "استيراد البيانات"
```

### 3. عرض النتائج

#### أ. لوحة التحكم:
```
http://localhost/TIaF-Report/dashboard.php
```

#### ب. التقارير المتخصصة:
```
http://localhost/TIaF-Report/reports/accounting.php
http://localhost/TIaF-Report/reports/conflict.php
http://localhost/TIaF-Report/reports/dispute.php
```

## حل المشاكل الشائعة

### مشكلة: "Unknown column" أثناء الاستيراد

**السبب:** عدم تطابق أسماء الأعمدة مع أسماء الحقول في قاعدة البيانات

**الحل:**
1. استخدم أداة التشخيص:
   ```
   http://localhost/TIaF-Report/debug_import.php
   ```

2. أعد إنشاء الجداول بالتحويل الصحيح:
   ```
   http://localhost/TIaF-Report/recreate_tables_with_mapping.php
   ```

### مشكلة: BOM في ملفات CSV

**السبب:** ملف CSV يحتوي على Byte Order Mark

**الحل:**
```
http://localhost/TIaF-Report/fix_csv_advanced.php
← ارفع ملف CSV
← حمل الملف المُصحح
← استورد الملف المُصحح
```

### مشكلة: "Request-URI Too Long"

**السبب:** ملف CSV كبير الحجم

**الحل:**
استخدم أداة إصلاح CSV المتقدمة بدلاً من البسيطة:
```
http://localhost/TIaF-Report/fix_csv_advanced.php
```

## هيكل الجداول

### جدول المحاسبة (accounting)
- **عدد الحقول:** 23 حقل + export_date
- **الاستخدام:** بيانات طلبات المحاسبة
- **ملف CSV:** AccountingEXPORT_*.csv

### جدول إنهاء النزاع (conflict)
- **عدد الحقول:** 35 حقل + export_date
- **الاستخدام:** بيانات طلبات إنهاء النزاع
- **ملف CSV:** ConflictEXPORT_*.csv

### جدول تسوية النزاع (dispute)
- **عدد الحقول:** 35 حقل + export_date
- **الاستخدام:** بيانات طلبات تسوية النزاع
- **ملف CSV:** DisputeEXPORT_*.csv

## تحويل أسماء الأعمدة

النظام يستخدم دالة موحدة لتحويل أسماء الأعمدة العربية إلى أسماء الحقول الإنجليزية:

| اسم العمود العربي | اسم الحقل الإنجليزي |
|-------------------|-------------------|
| رقم الطلب | request_number |
| تاريخ الإنشاء | creation_date |
| اسم المكلف | taxpayer_name |
| كود المأمورية | office_code |
| حالة الطلب | request_status |
| الضريبة المتوقعة | expected_tax |

## الأدوات المساعدة

### أدوات التشخيص:
- **اختبار النظام:** `test_system.php`
- **تشخيص الاستيراد:** `debug_import.php`
- **تحليل CSV:** `analyze_csv_structure.php`

### أدوات الإصلاح:
- **إصلاح CSV بسيط:** `fix_csv_simple.php`
- **إصلاح CSV متقدم:** `fix_csv_advanced.php`
- **تنظيف الملفات المؤقتة:** `cleanup_temp.php`

### أدوات إدارة الجداول:
- **إنشاء الجداول المرجعية:** `database_setup.php`
- **إنشاء الجداول بالتحويل:** `recreate_tables_with_mapping.php`
- **إنشاء من CSV:** `recreate_tables_from_csv.php`

## نصائح للاستخدام الأمثل

### 1. تحضير البيانات:
- احفظ ملفات CSV بترميز UTF-8 بدون BOM
- تأكد من تنسيق أسماء الملفات الصحيح
- راجع أسماء الأعمدة قبل الاستيراد

### 2. الاستيراد:
- ابدأ بملفات صغيرة للاختبار
- استخدم خيار "حذف البيانات السابقة" بحذر
- راجع رسائل الخطأ بعناية

### 3. الصيانة:
- قم بعمل نسخ احتياطية دورية
- نظف الملفات المؤقتة بانتظام
- راقب حجم قاعدة البيانات

## الدعم الفني

### في حالة وجود مشاكل:

1. **استخدم أدوات التشخيص** للحصول على تقرير مفصل
2. **راجع ملف CHANGELOG.md** للتحديثات الأخيرة
3. **تحقق من ملف DATABASE_STRUCTURE.md** لفهم هيكل قاعدة البيانات
4. **استخدم ملف CSV_IMPORT_GUIDE.md** لحل مشاكل الاستيراد

### ملفات التوثيق:
- `README.md` - معلومات عامة عن النظام
- `CHANGELOG.md` - سجل التغييرات والتحديثات
- `DATABASE_STRUCTURE.md` - هيكل قاعدة البيانات التفصيلي
- `CSV_IMPORT_GUIDE.md` - دليل حل مشاكل الاستيراد

## مثال عملي كامل

### السيناريو: استيراد بيانات المحاسبة لأول مرة

```bash
# 1. إعداد النظام
http://localhost/TIaF-Report/database_setup.php
http://localhost/TIaF-Report/recreate_tables_with_mapping.php

# 2. تحضير الملف
# تأكد من أن الملف يسمى: AccountingEXPORT_20250526.csv

# 3. اختبار الملف (اختياري)
http://localhost/TIaF-Report/debug_import.php
# ارفع الملف للتحليل

# 4. إصلاح الملف إذا لزم الأمر
http://localhost/TIaF-Report/fix_csv_advanced.php
# ارفع الملف وحمل النسخة المُصححة

# 5. الاستيراد
http://localhost/TIaF-Report/import_data.php
# اختر "جدول المحاسبة"
# ارفع الملف
# اضغط "استيراد البيانات"

# 6. التحقق من النتائج
http://localhost/TIaF-Report/dashboard.php
http://localhost/TIaF-Report/reports/accounting.php
```

### النتيجة المتوقعة:
```
✅ تم بنجاح: [عدد السجلات]
✅ أخطاء: 0
✅ تاريخ التصدير: 2025-05-26
```

---

**ملاحظة:** هذا الدليل يغطي الاستخدام الأساسي للنظام. للمزيد من التفاصيل، راجع ملفات التوثيق الأخرى.
