<?php
/**
 * اختبار النظام - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.4
 * @date 2025-06-17
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار النظام</h1>
            <p>فحص شامل لجميع مكونات نظام TIaF Report System</p>
        </div>
        
        <div class="content">
            <?php
            $tests = [];
            
            // اختبار الاتصال بقاعدة البيانات
            try {
                $pdo = getDBConnection();
                $tests[] = ['name' => 'الاتصال بقاعدة البيانات', 'status' => 'success', 'message' => 'تم الاتصال بنجاح'];
            } catch (Exception $e) {
                $tests[] = ['name' => 'الاتصال بقاعدة البيانات', 'status' => 'error', 'message' => 'فشل الاتصال: ' . $e->getMessage()];
            }
            
            // اختبار وجود الجداول
            $tables = ['accounting', 'conflict', 'dispute', 'classification', 'container', 'request_status'];
            foreach ($tables as $table) {
                try {
                    $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
                    $count = $stmt->fetchColumn();
                    $tests[] = ['name' => "جدول $table", 'status' => 'success', 'message' => "موجود ($count سجل)"];
                } catch (Exception $e) {
                    $tests[] = ['name' => "جدول $table", 'status' => 'error', 'message' => 'غير موجود أو خطأ: ' . $e->getMessage()];
                }
            }
            
            // اختبار الدوال المهمة
            $functions = [
                'extractExportDate' => 'استخراج تاريخ التصدير',
                'extractTableType' => 'استخراج نوع الجدول',
                'formatDate' => 'تنسيق التاريخ',
                'getDBConnection' => 'الاتصال بقاعدة البيانات'
            ];
            
            foreach ($functions as $func => $desc) {
                if (function_exists($func)) {
                    $tests[] = ['name' => "دالة $desc", 'status' => 'success', 'message' => 'موجودة'];
                } else {
                    $tests[] = ['name' => "دالة $desc", 'status' => 'error', 'message' => 'غير موجودة'];
                }
            }
            
            // اختبار الملفات المهمة
            $files = [
                'config/database.php' => 'ملف إعدادات قاعدة البيانات',
                'includes/functions.php' => 'ملف الدوال المساعدة',
                'includes/pdf_export.php' => 'ملف تصدير PDF',
                'assets/css/main.css' => 'ملف الأنماط الرئيسي',
                'assets/js/theme.js' => 'ملف إدارة الثيمات',
                'assets/js/dashboard.js' => 'ملف لوحة التحكم'
            ];
            
            foreach ($files as $file => $desc) {
                if (file_exists($file)) {
                    $size = round(filesize($file) / 1024, 2);
                    $tests[] = ['name' => $desc, 'status' => 'success', 'message' => "موجود ({$size} KB)"];
                } else {
                    $tests[] = ['name' => $desc, 'status' => 'error', 'message' => 'غير موجود'];
                }
            }
            
            // اختبار استخراج التاريخ
            if (function_exists('extractExportDate')) {
                $testDate = extractExportDate('AccountingEXPORT_20250526.csv');
                if ($testDate === '2025-05-26') {
                    $tests[] = ['name' => 'اختبار استخراج التاريخ', 'status' => 'success', 'message' => 'يعمل بشكل صحيح'];
                } else {
                    $tests[] = ['name' => 'اختبار استخراج التاريخ', 'status' => 'error', 'message' => "نتيجة خاطئة: $testDate"];
                }
            }
            
            // عرض النتائج
            $successCount = 0;
            $errorCount = 0;
            $warningCount = 0;
            
            foreach ($tests as $test) {
                $class = $test['status'];
                if ($class === 'success') $successCount++;
                elseif ($class === 'error') $errorCount++;
                else $warningCount++;
                
                echo "<div class='test-section $class'>";
                echo "<h4>" . ($class === 'success' ? '✅' : ($class === 'error' ? '❌' : '⚠️')) . " {$test['name']}</h4>";
                echo "<p>{$test['message']}</p>";
                echo "</div>";
            }
            
            // ملخص النتائج
            echo "<div class='test-section'>";
            echo "<h3>📊 ملخص النتائج</h3>";
            echo "<table>";
            echo "<tr><th>النوع</th><th>العدد</th><th>النسبة</th></tr>";
            $total = count($tests);
            echo "<tr><td>نجح ✅</td><td>$successCount</td><td>" . round(($successCount/$total)*100, 1) . "%</td></tr>";
            echo "<tr><td>فشل ❌</td><td>$errorCount</td><td>" . round(($errorCount/$total)*100, 1) . "%</td></tr>";
            echo "<tr><td>تحذير ⚠️</td><td>$warningCount</td><td>" . round(($warningCount/$total)*100, 1) . "%</td></tr>";
            echo "<tr><td><strong>المجموع</strong></td><td><strong>$total</strong></td><td><strong>100%</strong></td></tr>";
            echo "</table>";
            echo "</div>";
            
            // توصيات
            if ($errorCount > 0) {
                echo "<div class='test-section error'>";
                echo "<h3>🔧 توصيات الإصلاح</h3>";
                echo "<ul>";
                echo "<li>تأكد من تشغيل خادم MySQL</li>";
                echo "<li>تحقق من إعدادات قاعدة البيانات في config/database.php</li>";
                echo "<li>قم بتشغيل database_setup.php لإنشاء الجداول</li>";
                echo "<li>تأكد من وجود جميع الملفات المطلوبة</li>";
                echo "</ul>";
                echo "</div>";
            } else {
                echo "<div class='test-section success'>";
                echo "<h3>🎉 النظام جاهز للاستخدام!</h3>";
                echo "<p>جميع الاختبارات نجحت. يمكنك الآن استخدام النظام بشكل طبيعي.</p>";
                echo "</div>";
            }
            ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="dashboard.php" class="btn">لوحة التحكم</a>
                <a href="database_setup.php" class="btn">إعداد قاعدة البيانات</a>
            </div>
        </div>
    </div>
</body>
</html>
