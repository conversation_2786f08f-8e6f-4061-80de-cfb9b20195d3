<?php
/**
 * عرض محتويات الجداول المساعدة - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.1
 * @date 2025-06-17
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

try {
    $pdo = getDBConnection();
    
    // الحصول على بيانات الجداول المساعدة
    $classification_data = [];
    $container_data = [];
    $status_data = [];
    
    // جدول التصنيف
    try {
        $stmt = $pdo->query("SELECT * FROM classification ORDER BY office_code LIMIT 50");
        $classification_data = $stmt->fetchAll();
    } catch (Exception $e) {
        $classification_error = $e->getMessage();
    }
    
    // جدول الوعاء
    try {
        $stmt = $pdo->query("SELECT * FROM container ORDER BY container_code");
        $container_data = $stmt->fetchAll();
    } catch (Exception $e) {
        $container_error = $e->getMessage();
    }
    
    // جدول حالة الطلب
    try {
        $stmt = $pdo->query("SELECT * FROM request_status ORDER BY status_code");
        $status_data = $stmt->fetchAll();
    } catch (Exception $e) {
        $status_error = $e->getMessage();
    }
    
} catch (Exception $e) {
    $general_error = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الجداول المساعدة - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/main.css">
    <style>
        .table-container {
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .table th {
            background: #667eea;
            color: white;
            padding: 12px;
            text-align: center;
            font-weight: 600;
        }
        
        .table td {
            padding: 10px;
            border-bottom: 1px solid #eee;
            text-align: center;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .section-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0 10px 0;
        }
        
        .stats-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h1>📋 عرض الجداول المساعدة</h1>
                <p>عرض محتويات الجداول المرجعية (Mapping Tables)</p>
            </div>
            
            <div class="card-body">
                <?php if (isset($general_error)): ?>
                <div class="alert alert-danger">
                    <h4>❌ خطأ في الاتصال بقاعدة البيانات</h4>
                    <p><?php echo $general_error; ?></p>
                </div>
                <?php endif; ?>
                
                <!-- جدول التصنيف -->
                <div class="section-header">
                    <h3>🏢 جدول التصنيف (Classification)</h3>
                    <p>7 حقول: التصنيف، كود المأمورية، اسم المأمورية، المنطقة، المامورية، الاختصاص، مديري العموم</p>
                </div>
                
                <?php if (isset($classification_error)): ?>
                <div class="alert alert-danger">
                    <p>❌ خطأ في جدول التصنيف: <?php echo $classification_error; ?></p>
                </div>
                <?php elseif (!empty($classification_data)): ?>
                <div class="stats-card">
                    <strong>📊 إحصائيات:</strong> <?php echo count($classification_data); ?> سجل (عرض أول 50 سجل)
                </div>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>التصنيف</th>
                                <th>كود المأمورية</th>
                                <th>اسم المأمورية</th>
                                <th>المنطقة</th>
                                <th>المامورية</th>
                                <th>الاختصاص</th>
                                <th>مديري العموم</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($classification_data as $row): ?>
                            <tr>
                                <td><?php echo $row['id']; ?></td>
                                <td><?php echo htmlspecialchars($row['classification_type']); ?></td>
                                <td><?php echo htmlspecialchars($row['office_code']); ?></td>
                                <td><?php echo htmlspecialchars($row['office_name']); ?></td>
                                <td><?php echo htmlspecialchars($row['region']); ?></td>
                                <td><?php echo htmlspecialchars($row['office_branch']); ?></td>
                                <td><?php echo htmlspecialchars($row['specialization']); ?></td>
                                <td><?php echo htmlspecialchars($row['general_manager']); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="alert alert-warning">
                    <p>⚠️ لا توجد بيانات في جدول التصنيف</p>
                </div>
                <?php endif; ?>
                
                <!-- جدول الوعاء -->
                <div class="section-header">
                    <h3>📦 جدول الوعاء (Container)</h3>
                    <p>3 حقول: الوعاء، اسم الوعاء، التصنيف</p>
                </div>
                
                <?php if (isset($container_error)): ?>
                <div class="alert alert-danger">
                    <p>❌ خطأ في جدول الوعاء: <?php echo $container_error; ?></p>
                </div>
                <?php elseif (!empty($container_data)): ?>
                <div class="stats-card">
                    <strong>📊 إحصائيات:</strong> <?php echo count($container_data); ?> سجل
                </div>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>كود الوعاء</th>
                                <th>اسم الوعاء</th>
                                <th>التصنيف</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($container_data as $row): ?>
                            <tr>
                                <td><?php echo $row['id']; ?></td>
                                <td><?php echo htmlspecialchars($row['container_code']); ?></td>
                                <td><?php echo htmlspecialchars($row['container_name']); ?></td>
                                <td><?php echo htmlspecialchars($row['classification']); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="alert alert-warning">
                    <p>⚠️ لا توجد بيانات في جدول الوعاء</p>
                </div>
                <?php endif; ?>
                
                <!-- جدول حالة الطلب -->
                <div class="section-header">
                    <h3>📋 جدول حالة الطلب (Request Status)</h3>
                    <p>2 حقل: حالة الطلب، وصف الحالة</p>
                </div>
                
                <?php if (isset($status_error)): ?>
                <div class="alert alert-danger">
                    <p>❌ خطأ في جدول حالة الطلب: <?php echo $status_error; ?></p>
                </div>
                <?php elseif (!empty($status_data)): ?>
                <div class="stats-card">
                    <strong>📊 إحصائيات:</strong> <?php echo count($status_data); ?> سجل
                </div>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>كود الحالة</th>
                                <th>وصف الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($status_data as $row): ?>
                            <tr>
                                <td><?php echo $row['id']; ?></td>
                                <td><?php echo htmlspecialchars($row['status_code']); ?></td>
                                <td><?php echo htmlspecialchars($row['status_description']); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="alert alert-warning">
                    <p>⚠️ لا توجد بيانات في جدول حالة الطلب</p>
                </div>
                <?php endif; ?>
                
                <div style="text-align: center; margin-top: 30px;">
                    <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                    <a href="recreate_mapping_tables.php" class="btn btn-warning">إعادة إنشاء الجداول</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
