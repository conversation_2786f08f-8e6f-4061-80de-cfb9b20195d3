<?php
/**
 * تنظيف الملفات المؤقتة - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.4.2
 * @date 2025-06-17
 */

// تنظيف الملفات المؤقتة القديمة (أكثر من ساعة)
function cleanupTempFiles() {
    $tempDir = sys_get_temp_dir();
    $pattern = $tempDir . '/tiaf_fixed_*';
    $files = glob($pattern);
    $cleaned = 0;
    $errors = 0;
    
    foreach ($files as $file) {
        if (is_file($file)) {
            // حذف الملفات الأقدم من ساعة واحدة
            if (time() - filemtime($file) > 3600) {
                if (unlink($file)) {
                    $cleaned++;
                } else {
                    $errors++;
                }
            }
        }
    }
    
    return ['cleaned' => $cleaned, 'errors' => $errors, 'total' => count($files)];
}

// تشغيل التنظيف إذا تم استدعاء الملف مباشرة
if (basename($_SERVER['PHP_SELF']) === 'cleanup_temp.php') {
    $result = cleanupTempFiles();
    
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => "تم تنظيف {$result['cleaned']} ملف مؤقت",
        'details' => $result
    ]);
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تنظيف الملفات المؤقتة - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        #result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧹 تنظيف الملفات المؤقتة</h1>
            <p>إزالة الملفات المؤقتة القديمة لتوفير مساحة التخزين</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h3>📁 معلومات الملفات المؤقتة</h3>
                <p>يتم إنشاء ملفات مؤقتة عند استخدام أداة إصلاح CSV. هذه الملفات تحتاج للتنظيف بشكل دوري.</p>
                
                <h4>📍 موقع الملفات المؤقتة:</h4>
                <p><code><?php echo sys_get_temp_dir(); ?></code></p>
                
                <h4>🔍 الملفات الحالية:</h4>
                <?php
                $tempDir = sys_get_temp_dir();
                $pattern = $tempDir . '/tiaf_fixed_*';
                $files = glob($pattern);
                
                if (empty($files)) {
                    echo "<p>✅ لا توجد ملفات مؤقتة</p>";
                } else {
                    echo "<ul>";
                    foreach ($files as $file) {
                        $size = filesize($file);
                        $age = time() - filemtime($file);
                        $ageText = $age > 3600 ? round($age/3600, 1) . ' ساعة' : round($age/60) . ' دقيقة';
                        
                        echo "<li>";
                        echo basename($file) . " - ";
                        echo number_format($size) . " بايت - ";
                        echo "العمر: " . $ageText;
                        if ($age > 3600) {
                            echo " <span style='color: #dc3545;'>(قديم)</span>";
                        }
                        echo "</li>";
                    }
                    echo "</ul>";
                    
                    echo "<p><strong>إجمالي الملفات:</strong> " . count($files) . "</p>";
                    echo "<p><strong>إجمالي الحجم:</strong> " . number_format(array_sum(array_map('filesize', $files))) . " بايت</p>";
                }
                ?>
            </div>
            
            <div class="section">
                <h3>🧹 تنظيف الملفات</h3>
                <p>سيتم حذف الملفات الأقدم من ساعة واحدة فقط.</p>
                
                <button onclick="cleanupFiles()" class="btn btn-danger">🗑️ تنظيف الملفات القديمة</button>
                
                <div id="result"></div>
            </div>
            
            <div class="section">
                <h3>⚙️ التنظيف التلقائي</h3>
                <p>لتفعيل التنظيف التلقائي، أضف هذا السطر إلى crontab:</p>
                <code>0 * * * * php <?php echo __FILE__; ?></code>
                <p><small>هذا سيقوم بتنظيف الملفات كل ساعة</small></p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="fix_csv_advanced.php" class="btn">إصلاح CSV</a>
            </div>
        </div>
    </div>
    
    <script>
        function cleanupFiles() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'section';
            resultDiv.innerHTML = '<p>🔄 جاري التنظيف...</p>';
            
            fetch('cleanup_temp.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.className = 'section success';
                        resultDiv.innerHTML = `
                            <h4>✅ تم التنظيف بنجاح!</h4>
                            <p><strong>الملفات المحذوفة:</strong> ${data.details.cleaned}</p>
                            <p><strong>الأخطاء:</strong> ${data.details.errors}</p>
                            <p><strong>إجمالي الملفات:</strong> ${data.details.total}</p>
                        `;
                        
                        // إعادة تحميل الصفحة بعد 3 ثوان
                        setTimeout(() => {
                            location.reload();
                        }, 3000);
                    } else {
                        resultDiv.className = 'section error';
                        resultDiv.innerHTML = '<h4>❌ فشل التنظيف</h4><p>' + data.message + '</p>';
                    }
                })
                .catch(error => {
                    resultDiv.className = 'section error';
                    resultDiv.innerHTML = '<h4>❌ خطأ في الاتصال</h4><p>' + error.message + '</p>';
                });
        }
    </script>
</body>
</html>
