<?php
/**
 * أداة تقسيم ملفات CSV الكبيرة - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.4.6
 * @date 2025-06-17
 */

// زيادة حدود PHP
ini_set('memory_limit', '512M');
ini_set('max_execution_time', 300);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقسيم ملفات CSV الكبيرة - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            cursor: pointer;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-download {
            background: #17a2b8;
        }
        
        .file-input {
            width: 100%;
            padding: 15px;
            border: 2px dashed #667eea;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
            cursor: pointer;
        }
        
        .file-input:hover {
            border-color: #5a6fd8;
            background: #f8f9ff;
        }
        
        .download-links {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .download-links a {
            display: block;
            margin: 5px 0;
            padding: 8px 15px;
            background: #17a2b8;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }
        
        .download-links a:hover {
            background: #138496;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✂️ تقسيم ملفات CSV الكبيرة</h1>
            <p>تقسيم ملفات CSV الكبيرة إلى ملفات أصغر لسهولة الاستيراد</p>
        </div>
        
        <div class="content">
            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
                $file = $_FILES['csv_file'];
                $linesPerFile = intval($_POST['lines_per_file'] ?? 5000);
                
                if ($file['error'] === UPLOAD_ERR_OK) {
                    try {
                        echo "<div class='section'>";
                        echo "<h3>🔄 بدء عملية التقسيم</h3>";
                        echo "<p><strong>الملف:</strong> " . htmlspecialchars($file['name']) . "</p>";
                        echo "<p><strong>الحجم:</strong> " . number_format($file['size'] / 1024 / 1024, 2) . " ميجابايت</p>";
                        echo "<p><strong>عدد الأسطر لكل ملف:</strong> " . number_format($linesPerFile) . "</p>";
                        
                        // إنشاء مجلد للملفات المقسمة
                        $outputDir = 'temp/split_files';
                        if (!is_dir($outputDir)) {
                            mkdir($outputDir, 0777, true);
                        }
                        
                        // تنظيف المجلد من الملفات السابقة
                        $oldFiles = glob($outputDir . '/*');
                        foreach ($oldFiles as $oldFile) {
                            if (is_file($oldFile)) {
                                unlink($oldFile);
                            }
                        }
                        
                        // فتح الملف الأصلي
                        $handle = fopen($file['tmp_name'], 'r');
                        
                        // قراءة العناوين
                        $headers = fgetcsv($handle);
                        
                        // حساب إجمالي الأسطر
                        $totalLines = 0;
                        while (fgetcsv($handle) !== FALSE) {
                            $totalLines++;
                        }
                        rewind($handle);
                        fgetcsv($handle); // تخطي العناوين مرة أخرى
                        
                        echo "<p><strong>إجمالي السجلات:</strong> " . number_format($totalLines) . "</p>";
                        
                        // حساب عدد الملفات المطلوبة
                        $numberOfFiles = ceil($totalLines / $linesPerFile);
                        echo "<p><strong>عدد الملفات المتوقعة:</strong> $numberOfFiles</p>";
                        echo "</div>";
                        
                        // تقسيم الملف
                        $currentFile = 1;
                        $currentLineCount = 0;
                        $outputHandle = null;
                        $createdFiles = [];
                        
                        // استخراج اسم الملف الأساسي
                        $originalName = pathinfo($file['name'], PATHINFO_FILENAME);
                        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                        
                        while (($data = fgetcsv($handle)) !== FALSE) {
                            // إنشاء ملف جديد عند الحاجة
                            if ($currentLineCount === 0) {
                                if ($outputHandle) {
                                    fclose($outputHandle);
                                }
                                
                                $outputFileName = $outputDir . '/' . $originalName . '_part' . $currentFile . '.' . $extension;
                                $outputHandle = fopen($outputFileName, 'w');
                                
                                // كتابة العناوين في كل ملف
                                fputcsv($outputHandle, $headers);
                                
                                $createdFiles[] = [
                                    'name' => basename($outputFileName),
                                    'path' => $outputFileName,
                                    'part' => $currentFile
                                ];
                            }
                            
                            // كتابة البيانات
                            fputcsv($outputHandle, $data);
                            $currentLineCount++;
                            
                            // الانتقال للملف التالي عند الوصول للحد المطلوب
                            if ($currentLineCount >= $linesPerFile) {
                                $currentLineCount = 0;
                                $currentFile++;
                            }
                        }
                        
                        // إغلاق الملفات
                        if ($outputHandle) {
                            fclose($outputHandle);
                        }
                        fclose($handle);
                        
                        // عرض النتائج
                        echo "<div class='section success'>";
                        echo "<h3>✅ تم تقسيم الملف بنجاح!</h3>";
                        echo "<p><strong>عدد الملفات المُنشأة:</strong> " . count($createdFiles) . "</p>";
                        
                        // روابط التحميل
                        echo "<div class='download-links'>";
                        echo "<h4>📥 تحميل الملفات المقسمة:</h4>";
                        
                        foreach ($createdFiles as $fileInfo) {
                            $fileSize = number_format(filesize($fileInfo['path']) / 1024 / 1024, 2);
                            echo "<a href='download_split_file.php?file=" . urlencode($fileInfo['name']) . "' target='_blank'>";
                            echo "📄 " . $fileInfo['name'] . " (الجزء " . $fileInfo['part'] . " - $fileSize ميجابايت)";
                            echo "</a>";
                        }
                        
                        echo "</div>";
                        echo "</div>";
                        
                        // تعليمات الاستيراد
                        echo "<div class='section warning'>";
                        echo "<h3>📋 تعليمات الاستيراد</h3>";
                        echo "<ol>";
                        echo "<li><strong>حمل كل ملف على حدة</strong> من الروابط أعلاه</li>";
                        echo "<li><strong>استورد الملفات بالترتيب</strong> (الجزء 1، ثم الجزء 2، إلخ)</li>";
                        echo "<li><strong>استخدم خيار 'عدم حذف البيانات السابقة'</strong> للملفات بعد الأول</li>";
                        echo "<li><strong>تأكد من نجاح استيراد كل ملف</strong> قبل الانتقال للتالي</li>";
                        echo "</ol>";
                        echo "</div>";
                        
                    } catch (Exception $e) {
                        echo "<div class='section error'>";
                        echo "<h3>❌ خطأ في التقسيم</h3>";
                        echo "<p>" . $e->getMessage() . "</p>";
                        echo "</div>";
                    }
                } else {
                    echo "<div class='section error'>";
                    echo "<h3>❌ خطأ في رفع الملف</h3>";
                    echo "<p>كود الخطأ: " . $file['error'] . "</p>";
                    echo "</div>";
                }
            }
            ?>
            
            <!-- نموذج تقسيم الملف -->
            <div class="section">
                <h3>✂️ تقسيم ملف CSV</h3>
                <form method="post" enctype="multipart/form-data">
                    <div>
                        <label><strong>عدد الأسطر لكل ملف:</strong></label>
                        <select name="lines_per_file" style="width: 100%; padding: 10px; margin: 10px 0;">
                            <option value="1000">1,000 سطر (للملفات الكبيرة جداً)</option>
                            <option value="2500">2,500 سطر (للملفات الكبيرة)</option>
                            <option value="5000" selected>5,000 سطر (موصى به)</option>
                            <option value="10000">10,000 سطر (للملفات المتوسطة)</option>
                            <option value="25000">25,000 سطر (للملفات الصغيرة)</option>
                        </select>
                    </div>
                    
                    <div class="file-input">
                        <input type="file" name="csv_file" accept=".csv" required>
                        <p>اختر ملف CSV المراد تقسيمه</p>
                    </div>
                    
                    <button type="submit" class="btn btn-success">✂️ تقسيم الملف</button>
                </form>
            </div>
            
            <!-- معلومات مفيدة -->
            <div class="section">
                <h3>💡 نصائح لتقسيم الملفات</h3>
                <h4>اختيار عدد الأسطر المناسب:</h4>
                <ul>
                    <li><strong>1,000 - 2,500 سطر:</strong> للملفات الكبيرة جداً (أكثر من 50 ميجابايت)</li>
                    <li><strong>5,000 سطر:</strong> الخيار الأمثل لمعظم الملفات</li>
                    <li><strong>10,000 - 25,000 سطر:</strong> للملفات الصغيرة والمتوسطة</li>
                </ul>
                
                <h4>مميزات التقسيم:</h4>
                <ul>
                    <li><strong>تجنب مشاكل الحجم:</strong> تجاوز حدود رفع الملفات</li>
                    <li><strong>استيراد أسرع:</strong> معالجة أسرع للملفات الصغيرة</li>
                    <li><strong>أمان أكبر:</strong> في حالة فشل الاستيراد، لن تفقد كل البيانات</li>
                    <li><strong>مرونة أكثر:</strong> يمكن استيراد الملفات في أوقات مختلفة</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="large_file_import.php" class="btn">استيراد الملفات الكبيرة</a>
                <a href="import_data.php" class="btn">الاستيراد العادي</a>
            </div>
        </div>
    </div>
    
    <script>
        // تحسين واجهة رفع الملفات
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.querySelector('input[type="file"]');
            const fileInputDiv = document.querySelector('.file-input');
            
            fileInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    const file = this.files[0];
                    const sizeMB = (file.size / 1024 / 1024).toFixed(2);
                    fileInputDiv.innerHTML = `
                        <input type="file" name="csv_file" accept=".csv" required style="display: none;">
                        <p><strong>الملف المحدد:</strong> ${file.name}</p>
                        <p><strong>الحجم:</strong> ${sizeMB} ميجابايت</p>
                        <button type="button" onclick="location.reload();" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px;">تغيير الملف</button>
                    `;
                }
            });
        });
    </script>
</body>
</html>
