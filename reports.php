<?php
/**
 * صفحة التقارير الرئيسية - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 2.0
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // جلب تواريخ التصدير المتاحة
    $export_dates = [];
    $tables = ['accounting', 'conflict', 'dispute'];
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT DISTINCT export_date FROM `$table` ORDER BY export_date DESC");
        $dates = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $export_dates[$table] = $dates;
    }
    
    // جلب إحصائيات سريعة
    $stats = [];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) as total, COUNT(DISTINCT export_date) as dates FROM `$table`");
        $stats[$table] = $stmt->fetch();
    }
    
} catch (Exception $e) {
    $error_message = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - TIaF Report System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Tajawal', 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 40px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 25px;
            text-align: center;
            border-left: 5px solid #667eea;
        }
        
        .stat-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .report-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #667eea;
        }
        
        .report-card h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .date-selector {
            margin-bottom: 20px;
        }
        
        .date-selector label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #495057;
        }
        
        .date-selector select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
        }
        
        .date-selector select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            width: 100%;
            text-align: center;
            margin-top: 15px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .dates-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .dates-info h5 {
            color: #1565c0;
            margin-bottom: 10px;
        }
        
        .dates-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .date-tag {
            background: #1976d2;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
        }
        
        .navigation {
            text-align: center;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #e9ecef;
        }
        
        .navigation a {
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 التقارير</h1>
            <p>نظام تقارير الضرائب والرسوم - اختر التقرير وتاريخ التصدير</p>
        </div>
        
        <div class="content">
            <?php if (isset($error_message)): ?>
                <div class="alert alert-error">
                    ❌ خطأ في الاتصال بقاعدة البيانات: <?= htmlspecialchars($error_message) ?>
                </div>
            <?php else: ?>
                
                <!-- إحصائيات سريعة -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>📊 طلبات المحاسبة</h3>
                        <div class="stat-number"><?= number_format($stats['accounting']['total']) ?></div>
                        <div class="stat-label">إجمالي السجلات</div>
                        <div class="stat-label"><?= $stats['accounting']['dates'] ?> تاريخ تصدير</div>
                    </div>
                    
                    <div class="stat-card">
                        <h3>⚖️ طلبات إنهاء النزاع</h3>
                        <div class="stat-number"><?= number_format($stats['conflict']['total']) ?></div>
                        <div class="stat-label">إجمالي السجلات</div>
                        <div class="stat-label"><?= $stats['conflict']['dates'] ?> تاريخ تصدير</div>
                    </div>
                    
                    <div class="stat-card">
                        <h3>🤝 طلبات تسوية النزاع</h3>
                        <div class="stat-number"><?= number_format($stats['dispute']['total']) ?></div>
                        <div class="stat-label">إجمالي السجلات</div>
                        <div class="stat-label"><?= $stats['dispute']['dates'] ?> تاريخ تصدير</div>
                    </div>
                </div>
                
                <!-- شبكة التقارير -->
                <div class="reports-grid">
                    <!-- تقرير المحاسبة -->
                    <div class="report-card">
                        <h3>📊 تقرير طلبات المحاسبة</h3>
                        
                        <form action="reports/accounting.php" method="GET">
                            <div class="date-selector">
                                <label for="export_date_accounting">اختر تاريخ التصدير:</label>
                                <select name="export_date" id="export_date_accounting" required>
                                    <option value="">-- اختر التاريخ --</option>
                                    <?php foreach ($export_dates['accounting'] as $date): ?>
                                        <option value="<?= $date ?>"><?= date('d-m-Y', strtotime($date)) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <button type="submit" class="btn btn-success">عرض تقرير المحاسبة</button>
                        </form>
                        
                        <?php if (!empty($export_dates['accounting'])): ?>
                            <div class="dates-info">
                                <h5>تواريخ التصدير المتاحة:</h5>
                                <div class="dates-list">
                                    <?php foreach (array_slice($export_dates['accounting'], 0, 5) as $date): ?>
                                        <span class="date-tag"><?= date('d-m-Y', strtotime($date)) ?></span>
                                    <?php endforeach; ?>
                                    <?php if (count($export_dates['accounting']) > 5): ?>
                                        <span class="date-tag">+<?= count($export_dates['accounting']) - 5 ?> أخرى</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- تقرير إنهاء النزاع -->
                    <div class="report-card">
                        <h3>⚖️ تقرير طلبات إنهاء النزاع</h3>
                        
                        <form action="reports/conflict.php" method="GET">
                            <div class="date-selector">
                                <label for="export_date_conflict">اختر تاريخ التصدير:</label>
                                <select name="export_date" id="export_date_conflict" required>
                                    <option value="">-- اختر التاريخ --</option>
                                    <?php foreach ($export_dates['conflict'] as $date): ?>
                                        <option value="<?= $date ?>"><?= date('d-m-Y', strtotime($date)) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <button type="submit" class="btn btn-success">عرض تقرير إنهاء النزاع</button>
                        </form>
                        
                        <?php if (!empty($export_dates['conflict'])): ?>
                            <div class="dates-info">
                                <h5>تواريخ التصدير المتاحة:</h5>
                                <div class="dates-list">
                                    <?php foreach (array_slice($export_dates['conflict'], 0, 5) as $date): ?>
                                        <span class="date-tag"><?= date('d-m-Y', strtotime($date)) ?></span>
                                    <?php endforeach; ?>
                                    <?php if (count($export_dates['conflict']) > 5): ?>
                                        <span class="date-tag">+<?= count($export_dates['conflict']) - 5 ?> أخرى</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- تقرير تسوية النزاع -->
                    <div class="report-card">
                        <h3>🤝 تقرير طلبات تسوية النزاع</h3>
                        
                        <form action="reports/dispute.php" method="GET">
                            <div class="date-selector">
                                <label for="export_date_dispute">اختر تاريخ التصدير:</label>
                                <select name="export_date" id="export_date_dispute" required>
                                    <option value="">-- اختر التاريخ --</option>
                                    <?php foreach ($export_dates['dispute'] as $date): ?>
                                        <option value="<?= $date ?>"><?= date('d-m-Y', strtotime($date)) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <button type="submit" class="btn btn-success">عرض تقرير تسوية النزاع</button>
                        </form>
                        
                        <?php if (!empty($export_dates['dispute'])): ?>
                            <div class="dates-info">
                                <h5>تواريخ التصدير المتاحة:</h5>
                                <div class="dates-list">
                                    <?php foreach (array_slice($export_dates['dispute'], 0, 5) as $date): ?>
                                        <span class="date-tag"><?= date('d-m-Y', strtotime($date)) ?></span>
                                    <?php endforeach; ?>
                                    <?php if (count($export_dates['dispute']) > 5): ?>
                                        <span class="date-tag">+<?= count($export_dates['dispute']) - 5 ?> أخرى</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- التقرير المجمع حسب التخصص -->
                <div class="report-card">
                    <h3>📈 التقرير المجمع حسب التخصص</h3>

                    <form action="reports/specialization_summary.php" method="GET">
                        <div class="form-group">
                            <label>📅 تاريخ التصدير:</label>
                            <select name="export_date" required>
                                <option value="">-- اختر تاريخ التصدير --</option>
                                <?php
                                // دمج جميع تواريخ التصدير من التقارير الثلاثة
                                $all_dates = array_unique(array_merge(
                                    $export_dates['accounting'] ?? [],
                                    $export_dates['conflict'] ?? [],
                                    $export_dates['dispute'] ?? []
                                ));
                                rsort($all_dates);
                                foreach ($all_dates as $date):
                                ?>
                                    <option value="<?= htmlspecialchars($date) ?>">
                                        <?= date('d-m-Y', strtotime($date)) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            📊 عرض التقرير المجمع
                        </button>
                    </form>

                    <div class="report-description">
                        <p>تقرير شامل يجمع بيانات جميع التقارير (المحاسبة، النزاع، المنازعة) ويعرضها مقسمة حسب التخصص والمنطقة</p>
                        <div class="report-features">
                            <span>📊 بيانات مجمعة من جميع التقارير</span>
                            <span>🎯 تقسيم حسب التخصص</span>
                            <span>📍 تقسيم حسب المنطقة</span>
                            <span>📈 نسب الإنجاز</span>
                        </div>
                    </div>
                </div>

            <?php endif; ?>

            <div class="navigation">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="import_data.php" class="btn btn-info">استيراد البيانات</a>
                <a href="search.php" class="btn btn-info">البحث في البيانات</a>
            </div>
        </div>
    </div>
</body>
</html>
