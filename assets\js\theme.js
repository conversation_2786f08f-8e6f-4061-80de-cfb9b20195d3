/**
 * إدارة الثيمات (وضع الليل/النهار) - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.3
 * @date 2025-06-17
 */

// إعدادات الثيم
const THEME_CONFIG = {
    STORAGE_KEY: 'tiaf-theme',
    THEMES: {
        LIGHT: 'light',
        DARK: 'dark'
    },
    ICONS: {
        LIGHT: '🌙',
        DARK: '☀️'
    }
};

/**
 * فئة إدارة الثيمات
 */
class ThemeManager {
    constructor() {
        this.currentTheme = this.getSavedTheme();
        this.init();
    }

    /**
     * تهيئة مدير الثيمات
     */
    init() {
        this.createThemeToggle();
        this.applyTheme(this.currentTheme);
        this.bindEvents();
    }

    /**
     * إنشاء زر تبديل الثيم
     */
    createThemeToggle() {
        // التحقق من وجود الزر مسبقاً
        if (document.getElementById('themeToggle')) {
            return;
        }

        const toggleButton = document.createElement('button');
        toggleButton.id = 'themeToggle';
        toggleButton.className = 'theme-toggle';
        toggleButton.setAttribute('aria-label', 'تبديل وضع الليل/النهار');
        toggleButton.setAttribute('title', 'تبديل وضع الليل/النهار');
        
        this.updateToggleIcon(toggleButton);
        
        document.body.appendChild(toggleButton);
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        const toggleButton = document.getElementById('themeToggle');
        if (toggleButton) {
            toggleButton.addEventListener('click', () => this.toggleTheme());
        }

        // اختصار لوحة المفاتيح (Ctrl + Shift + T)
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                this.toggleTheme();
            }
        });

        // مراقبة تغييرات النظام
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addListener((e) => {
                if (!this.getSavedTheme()) {
                    this.applyTheme(e.matches ? THEME_CONFIG.THEMES.DARK : THEME_CONFIG.THEMES.LIGHT);
                }
            });
        }
    }

    /**
     * تبديل الثيم
     */
    toggleTheme() {
        const newTheme = this.currentTheme === THEME_CONFIG.THEMES.DARK 
            ? THEME_CONFIG.THEMES.LIGHT 
            : THEME_CONFIG.THEMES.DARK;
        
        this.setTheme(newTheme);
    }

    /**
     * تعيين ثيم محدد
     * @param {string} theme - نوع الثيم
     */
    setTheme(theme) {
        this.currentTheme = theme;
        this.applyTheme(theme);
        this.saveTheme(theme);
        this.updateToggleIcon();
        this.triggerThemeChangeEvent(theme);
    }

    /**
     * تطبيق الثيم على الصفحة
     * @param {string} theme - نوع الثيم
     */
    applyTheme(theme) {
        const body = document.body;
        
        if (theme === THEME_CONFIG.THEMES.DARK) {
            body.classList.add('dark-mode');
        } else {
            body.classList.remove('dark-mode');
        }

        // تحديث متغيرات CSS
        this.updateCSSVariables(theme);
    }

    /**
     * تحديث متغيرات CSS حسب الثيم
     * @param {string} theme - نوع الثيم
     */
    updateCSSVariables(theme) {
        const root = document.documentElement;
        
        if (theme === THEME_CONFIG.THEMES.DARK) {
            root.style.setProperty('--bg-color', '#2c3e50');
            root.style.setProperty('--text-color', '#ecf0f1');
            root.style.setProperty('--card-bg', '#34495e');
            root.style.setProperty('--border-color', '#4a5f7a');
        } else {
            root.style.setProperty('--bg-color', '#f5f7fa');
            root.style.setProperty('--text-color', '#2c3e50');
            root.style.setProperty('--card-bg', '#ffffff');
            root.style.setProperty('--border-color', '#dee2e6');
        }
    }

    /**
     * تحديث أيقونة زر التبديل
     * @param {HTMLElement} button - زر التبديل (اختياري)
     */
    updateToggleIcon(button = null) {
        const toggleButton = button || document.getElementById('themeToggle');
        if (!toggleButton) return;

        const icon = this.currentTheme === THEME_CONFIG.THEMES.DARK 
            ? THEME_CONFIG.ICONS.DARK 
            : THEME_CONFIG.ICONS.LIGHT;
        
        toggleButton.textContent = icon;
        
        const title = this.currentTheme === THEME_CONFIG.THEMES.DARK 
            ? 'تبديل إلى الوضع النهاري' 
            : 'تبديل إلى الوضع الليلي';
        
        toggleButton.setAttribute('title', title);
        toggleButton.setAttribute('aria-label', title);
    }

    /**
     * حفظ الثيم في التخزين المحلي
     * @param {string} theme - نوع الثيم
     */
    saveTheme(theme) {
        try {
            localStorage.setItem(THEME_CONFIG.STORAGE_KEY, theme);
        } catch (e) {
            console.warn('لا يمكن حفظ إعدادات الثيم:', e);
        }
    }

    /**
     * استرداد الثيم المحفوظ
     * @returns {string} نوع الثيم المحفوظ أو الافتراضي
     */
    getSavedTheme() {
        try {
            const saved = localStorage.getItem(THEME_CONFIG.STORAGE_KEY);
            if (saved && Object.values(THEME_CONFIG.THEMES).includes(saved)) {
                return saved;
            }
        } catch (e) {
            console.warn('لا يمكن قراءة إعدادات الثيم:', e);
        }

        // إرجاع الثيم الافتراضي حسب تفضيلات النظام
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return THEME_CONFIG.THEMES.DARK;
        }
        
        return THEME_CONFIG.THEMES.LIGHT;
    }

    /**
     * إطلاق حدث تغيير الثيم
     * @param {string} theme - نوع الثيم الجديد
     */
    triggerThemeChangeEvent(theme) {
        const event = new CustomEvent('themeChanged', {
            detail: { theme, previousTheme: this.currentTheme }
        });
        document.dispatchEvent(event);
    }

    /**
     * الحصول على الثيم الحالي
     * @returns {string} نوع الثيم الحالي
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * التحقق من كون الثيم الحالي مظلم
     * @returns {boolean}
     */
    isDarkMode() {
        return this.currentTheme === THEME_CONFIG.THEMES.DARK;
    }
}

// إنشاء مثيل مدير الثيمات
let themeManager;

/**
 * تهيئة مدير الثيمات
 */
function initThemeManager() {
    if (!themeManager) {
        themeManager = new ThemeManager();
    }
    return themeManager;
}

/**
 * دالة مساعدة لتبديل الثيم (للاستخدام في HTML)
 */
function toggleTheme() {
    if (themeManager) {
        themeManager.toggleTheme();
    }
}

/**
 * دالة مساعدة للحصول على الثيم الحالي
 */
function getCurrentTheme() {
    return themeManager ? themeManager.getCurrentTheme() : THEME_CONFIG.THEMES.LIGHT;
}

/**
 * دالة مساعدة للتحقق من الوضع المظلم
 */
function isDarkMode() {
    return themeManager ? themeManager.isDarkMode() : false;
}

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    initThemeManager();
});

// تصدير للاستخدام كوحدة
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        ThemeManager,
        initThemeManager,
        toggleTheme,
        getCurrentTheme,
        isDarkMode,
        THEME_CONFIG
    };
}
