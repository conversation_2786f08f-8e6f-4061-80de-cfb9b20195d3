<?php
/**
 * إعادة إنشاء الجداول الأساسية بالعدد الصحيح للحقول - TIaF Report System
 * 
 * جدول accounting: 23 حقل + export_date
 * جدول conflict: 39 حقل + export_date  
 * جدول dispute: 39 حقل + export_date
 * 
 * <AUTHOR> Development Team
 * @version 1.4.3
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة إنشاء الجداول الأساسية - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Ta<PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 إعادة إنشاء الجداول الأساسية</h1>
            <p>إنشاء الجداول بالعدد الصحيح للحقول مع أسماء باللغتين</p>
        </div>
        
        <div class="content">
            <?php
            try {
                $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                echo "<div class='section success'>";
                echo "<h3>✅ تم الاتصال بقاعدة البيانات بنجاح</h3>";
                echo "</div>";
                
                // حذف الجداول الموجودة
                echo "<div class='section warning'>";
                echo "<h3>🗑️ حذف الجداول الموجودة</h3>";
                
                $tables = ['accounting', 'conflict', 'dispute'];
                foreach ($tables as $table) {
                    $pdo->exec("DROP TABLE IF EXISTS `$table`");
                    echo "<p>🗑️ تم حذف جدول $table</p>";
                }
                echo "</div>";
                
                // إنشاء جدول المحاسبة (23 حقل + export_date)
                echo "<div class='section'>";
                echo "<h3>📊 إنشاء جدول المحاسبة (23 حقل)</h3>";
                
                $sql_accounting = "
                CREATE TABLE `accounting` (
                    `request_number` varchar(50) COMMENT 'رقم الطلب | Request Number',
                    `creation_date` datetime COMMENT 'تاريخ الإنشاء | Creation Date',
                    `created_by` varchar(100) COMMENT 'منشئ الطلب | Created By',
                    `modification_date` datetime COMMENT 'تاريخ التعديل | Modification Date',
                    `modified_by` varchar(100) COMMENT 'معدل الطلب | Modified By',
                    `request_status` varchar(10) COMMENT 'حالة الطلب | Request Status',
                    `status_description` varchar(255) COMMENT 'وصف الحالة | Status Description',
                    `request_type` varchar(10) COMMENT 'نوع الطلب | Request Type',
                    `request_type_name` varchar(255) COMMENT 'اسم نوع الطلب | Request Type Name',
                    `tax_registration_number` varchar(50) COMMENT 'رقم التسجيل الضريبي | Tax Registration Number',
                    `office_code` varchar(10) COMMENT 'كود المأمورية | Office Code',
                    `office_name` varchar(255) COMMENT 'اسم المأمورية | Office Name',
                    `taxpayer_name` varchar(255) COMMENT 'اسم المكلف | Taxpayer Name',
                    `address` text COMMENT 'العنوان | Address',
                    `phone_number` varchar(50) COMMENT 'رقم الهاتف | Phone Number',
                    `email` varchar(255) COMMENT 'البريد الإلكتروني | Email',
                    `accounting_type` varchar(10) COMMENT 'نوع المحاسبة | Accounting Type',
                    `accounting_type_description` varchar(255) COMMENT 'وصف نوع المحاسبة | Accounting Type Description',
                    `container` varchar(10) COMMENT 'الوعاء | Container',
                    `container_name` varchar(255) COMMENT 'اسم الوعاء | Container Name',
                    `period` varchar(20) COMMENT 'الفترة | Period',
                    `expected_tax` decimal(15,2) COMMENT 'الضريبة المتوقعة | Expected Tax',
                    `tax_according_to_law` decimal(15,2) COMMENT 'الضريبة طبقاً للقانون | Tax According To Law',
                    `export_date` date NOT NULL COMMENT 'تاريخ التصدير | Export Date',
                    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    
                    KEY `idx_export_date` (`export_date`),
                    KEY `idx_request_number` (`request_number`),
                    KEY `idx_office_code` (`office_code`),
                    KEY `idx_taxpayer_name` (`taxpayer_name`),
                    KEY `idx_request_status` (`request_status`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
                COMMENT='جدول طلبات المحاسبة - 23 حقل أساسي + export_date'";
                
                $pdo->exec($sql_accounting);
                echo "<p>✅ تم إنشاء جدول المحاسبة بنجاح (23 حقل + export_date)</p>";

                // عرض عدد الحقول
                $stmt = $pdo->query("DESCRIBE accounting");
                $columns = $stmt->fetchAll();
                echo "<p>📊 إجمالي الحقول: " . count($columns) . "</p>";
                echo "</div>";

                // إنشاء جدول إنهاء النزاع (39 حقل + export_date)
                echo "<div class='section'>";
                echo "<h3>⚖️ إنشاء جدول إنهاء النزاع (39 حقل)</h3>";

                $sql_conflict = "
                CREATE TABLE `conflict` (
                    `request_number` varchar(50) COMMENT 'رقم الطلب | Request Number',
                    `creation_date` datetime COMMENT 'تاريخ الإنشاء | Creation Date',
                    `created_by` varchar(100) COMMENT 'منشئ الطلب | Created By',
                    `modification_date` datetime COMMENT 'تاريخ التعديل | Modification Date',
                    `modified_by` varchar(100) COMMENT 'معدل الطلب | Modified By',
                    `request_status` varchar(10) COMMENT 'حالة الطلب | Request Status',
                    `status_description` varchar(255) COMMENT 'وصف الحالة | Status Description',
                    `request_type` varchar(10) COMMENT 'نوع الطلب | Request Type',
                    `request_type_name` varchar(255) COMMENT 'اسم نوع الطلب | Request Type Name',
                    `tax_registration_number` varchar(50) COMMENT 'رقم التسجيل الضريبي | Tax Registration Number',
                    `office_code` varchar(10) COMMENT 'كود المأمورية | Office Code',
                    `office_name` varchar(255) COMMENT 'اسم المأمورية | Office Name',
                    `taxpayer_name` varchar(255) COMMENT 'اسم المكلف | Taxpayer Name',
                    `address` text COMMENT 'العنوان | Address',
                    `phone_number` varchar(50) COMMENT 'رقم الهاتف | Phone Number',
                    `email` varchar(255) COMMENT 'البريد الإلكتروني | Email',
                    `accounting_type` varchar(10) COMMENT 'نوع المحاسبة | Accounting Type',
                    `accounting_type_description` varchar(255) COMMENT 'وصف نوع المحاسبة | Accounting Type Description',
                    `container` varchar(10) COMMENT 'الوعاء | Container',
                    `container_name` varchar(255) COMMENT 'اسم الوعاء | Container Name',
                    `period` varchar(20) COMMENT 'الفترة | Period',
                    `dispute_stage` varchar(10) COMMENT 'مرحلة النزاع | Dispute Stage',
                    `dispute_stage_description` varchar(255) COMMENT 'وصف مرحلة النزاع | Dispute Stage Description',
                    `case_number` varchar(100) COMMENT 'رقم القضية | Case Number',
                    `dispute_authority` varchar(10) COMMENT 'جهة النزاع | Dispute Authority',
                    `dispute_authority_name` varchar(255) COMMENT 'اسم جهة النزاع | Dispute Authority Name',
                    `other_authority` varchar(255) COMMENT 'جهة أخرى | Other Authority',
                    `tax_according_to_declaration` decimal(15,2) COMMENT 'الضريبة طبقاً للإقرار | Tax According To Declaration',
                    `tax_from_last_assessment` decimal(15,2) COMMENT 'الضريبة من آخر ربط | Tax From Last Assessment',
                    `last_assessment_year` varchar(10) COMMENT 'سنة آخر ربط | Last Assessment Year',
                    `tax_according_to_form` decimal(15,2) COMMENT 'الضريبة طبقاً للنموذج | Tax According To Form',
                    `tax_paid` decimal(15,2) COMMENT 'الضريبة المسددة | Tax Paid',
                    `tax_according_to_declaration_before_modification` decimal(15,2) COMMENT 'الضريبة طبقاً للإقرار قبل التعديل | Tax According To Declaration Before Modification',
                    `tax_from_last_assessment_before_modification` decimal(15,2) COMMENT 'الضريبة من آخر ربط قبل التعديل | Tax From Last Assessment Before Modification',
                    `tax_according_to_form_before_modification` decimal(15,2) COMMENT 'الضريبة طبقاً للنموذج قبل التعديل | Tax According To Form Before Modification',
                    `tax_paid_before_modification` decimal(15,2) COMMENT 'الضريبة المسددة قبل التعديل | Tax Paid Before Modification',
                    `expected_tax` decimal(15,2) COMMENT 'الضريبة المتوقعة | Expected Tax',
                    `tax_according_to_law` decimal(15,2) COMMENT 'الضريبة طبقاً للقانون | Tax According To Law',
                    `tax_due_payment` decimal(15,2) COMMENT 'الضريبة المستحقة | Tax Due Payment',
                    `export_date` date NOT NULL COMMENT 'تاريخ التصدير | Export Date',
                    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                    KEY `idx_export_date` (`export_date`),
                    KEY `idx_request_number` (`request_number`),
                    KEY `idx_office_code` (`office_code`),
                    KEY `idx_taxpayer_name` (`taxpayer_name`),
                    KEY `idx_request_status` (`request_status`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                COMMENT='جدول طلبات إنهاء النزاع - 39 حقل أساسي + export_date'";

                $pdo->exec($sql_conflict);
                echo "<p>✅ تم إنشاء جدول إنهاء النزاع بنجاح (39 حقل + export_date)</p>";

                // عرض عدد الحقول
                $stmt = $pdo->query("DESCRIBE conflict");
                $columns = $stmt->fetchAll();
                echo "<p>📊 إجمالي الحقول: " . count($columns) . "</p>";
                echo "</div>";

                // إنشاء جدول تسوية النزاع (39 حقل + export_date) - نفس هيكل conflict
                echo "<div class='section'>";
                echo "<h3>🤝 إنشاء جدول تسوية النزاع (39 حقل)</h3>";

                $sql_dispute = str_replace('CREATE TABLE `conflict`', 'CREATE TABLE `dispute`', $sql_conflict);
                $sql_dispute = str_replace('جدول طلبات إنهاء النزاع', 'جدول طلبات تسوية النزاع', $sql_dispute);

                $pdo->exec($sql_dispute);
                echo "<p>✅ تم إنشاء جدول تسوية النزاع بنجاح (39 حقل + export_date)</p>";

                // عرض عدد الحقول
                $stmt = $pdo->query("DESCRIBE dispute");
                $columns = $stmt->fetchAll();
                echo "<p>📊 إجمالي الحقول: " . count($columns) . "</p>";
                echo "</div>";

                // ملخص النتائج
                echo "<div class='section success'>";
                echo "<h3>🎉 تم إنشاء جميع الجداول بنجاح!</h3>";
                echo "<ul>";
                echo "<li>✅ جدول المحاسبة (accounting): 23 حقل + export_date</li>";
                echo "<li>✅ جدول إنهاء النزاع (conflict): 39 حقل + export_date</li>";
                echo "<li>✅ جدول تسوية النزاع (dispute): 39 حقل + export_date</li>";
                echo "</ul>";
                echo "<p>الجداول جاهزة الآن لاستيراد البيانات من ملفات CSV.</p>";
                echo "</div>";
                
            } catch (PDOException $e) {
                echo "<div class='section error'>";
                echo "<h3>❌ خطأ في قاعدة البيانات</h3>";
                echo "<p>" . $e->getMessage() . "</p>";
                echo "</div>";
            }
            ?>
            
            <div class="section success">
                <h3>✅ تم إنشاء جميع الجداول</h3>
                <p>تم إنشاء الجداول الثلاثة بالعدد الصحيح للحقول:</p>
                <ul>
                    <li><strong>جدول المحاسبة:</strong> 23 حقل + export_date (بناءً على AccountingEXPORT_20250526.csv)</li>
                    <li><strong>جدول إنهاء النزاع:</strong> 39 حقل + export_date (بناءً على ConflictEXPORT_20250526.csv)</li>
                    <li><strong>جدول تسوية النزاع:</strong> 39 حقل + export_date (بناءً على DisputeEXPORT_20250526.csv)</li>
                </ul>
            </div>
            
            <div class="section">
                <h3>📋 الحقول المنشأة</h3>

                <h4>جدول المحاسبة (23 حقل):</h4>
                <ol>
                    <li>رقم الطلب (Request Number)</li>
                    <li>تاريخ الإنشاء (Creation Date)</li>
                    <li>منشئ الطلب (Created By)</li>
                    <li>تاريخ التعديل (Modification Date)</li>
                    <li>معدل الطلب (Modified By)</li>
                    <li>حالة الطلب (Request Status)</li>
                    <li>وصف الحالة (Status Description)</li>
                    <li>نوع الطلب (Request Type)</li>
                    <li>اسم نوع الطلب (Request Type Name)</li>
                    <li>رقم التسجيل الضريبي (Tax Registration Number)</li>
                    <li>كود المأمورية (Office Code)</li>
                    <li>اسم المأمورية (Office Name)</li>
                    <li>اسم المكلف (Taxpayer Name)</li>
                    <li>العنوان (Address)</li>
                    <li>رقم الهاتف (Phone Number)</li>
                    <li>البريد الإلكتروني (Email)</li>
                    <li>نوع المحاسبة (Accounting Type)</li>
                    <li>وصف نوع المحاسبة (Accounting Type Description)</li>
                    <li>الوعاء (Container)</li>
                    <li>اسم الوعاء (Container Name)</li>
                    <li>الفترة (Period)</li>
                    <li>الضريبة المتوقعة (Expected Tax)</li>
                    <li>الضريبة طبقاً للقانون (Tax According To Law)</li>
                    <li><strong>تاريخ التصدير (Export Date)</strong> - مستخرج من اسم الملف</li>
                </ol>

                <h4>جداول إنهاء النزاع وتسوية النزاع (39 حقل لكل منهما):</h4>
                <p>تحتوي على جميع الحقول السابقة بالإضافة إلى:</p>
                <ol start="22">
                    <li>مرحلة النزاع (Dispute Stage)</li>
                    <li>وصف مرحلة النزاع (Dispute Stage Description)</li>
                    <li>رقم القضية (Case Number)</li>
                    <li>جهة النزاع (Dispute Authority)</li>
                    <li>اسم جهة النزاع (Dispute Authority Name)</li>
                    <li>جهة أخرى (Other Authority)</li>
                    <li>الضريبة طبقاً للإقرار (Tax According To Declaration)</li>
                    <li>الضريبة من آخر ربط (Tax From Last Assessment)</li>
                    <li>سنة آخر ربط (Last Assessment Year)</li>
                    <li>الضريبة طبقاً للنموذج (Tax According To Form)</li>
                    <li>الضريبة المسددة (Tax Paid)</li>
                    <li>الضريبة طبقاً للإقرار قبل التعديل (Tax According To Declaration Before Modification)</li>
                    <li>الضريبة من آخر ربط قبل التعديل (Tax From Last Assessment Before Modification)</li>
                    <li>الضريبة طبقاً للنموذج قبل التعديل (Tax According To Form Before Modification)</li>
                    <li>الضريبة المسددة قبل التعديل (Tax Paid Before Modification)</li>
                    <li>الضريبة المستحقة (Tax Due Payment)</li>
                    <li><strong>تاريخ التصدير (Export Date)</strong> - مستخرج من اسم الملف</li>
                </ol>
            </div>
            
            <div class="section">
                <h3>🔄 الخطوات التالية</h3>
                <p>لإكمال إنشاء جداول Conflict و Dispute بـ 39 حقل، نحتاج إلى:</p>
                <ol>
                    <li>فحص ملفات <code>ConflictEXPORT_20250526.csv</code> و <code>DisputeEXPORT_20250526.csv</code></li>
                    <li>تحديد أسماء الحقول الإضافية (16 حقل إضافي لكل جدول)</li>
                    <li>إنشاء الجداول بالهيكل الصحيح</li>
                </ol>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="debug_import.php" class="btn">تشخيص الاستيراد</a>
                <a href="import_data.php" class="btn">استيراد البيانات</a>
            </div>
        </div>
    </div>
</body>
</html>
