@startuml
skinparam backgroundColor #f9f9f9
skinparam classFontColor #1a237e
skinparam classAttributeIconSize 0

entity "Accounting/Conflict/Dispute (Main Table)" as MainTable {
  +request_id : رقم
  +status_id : رقم (مرجعية)
  +office_code : رقم (مرجعية)
  +container_id : رقم (مرجعية)
  ...
}

entity "جدول حالة الطلب" as StatusTable {
  +حالة الطلب : رقم (مفتاح)
  +وصف الحالة : نص
}

entity "جدول التصنيف" as ClassificationTable {
  +كود المأمورية : رقم (مفتاح)
  +التصنيف : نص
  ...
}

entity "جدول الوعاء" as ContainerTable {
  +الوعاء : رقم (مفتاح)
  +اسم الوعاء : نص
  +التصنيف : نص (مرجعية)
}

MainTable::status_id -- StatusTable::حالة الطلب
MainTable::office_code -- ClassificationTable::كود المأمورية
MainTable::container_id -- ContainerTable::الوعاء
ContainerTable::التصنيف -- ClassificationTable::التصنيف
@enduml
