<?php
/**
 * إعادة إنشاء الجداول باستخدام تحويل أسماء الأعمدة من debug_import.php
 * TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.4.5
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

// دالة تحويل أسماء الأعمدة (نفس الموجودة في debug_import.php)
function getColumnMapping() {
    return [
        // الأعمدة الأساسية
        'رقم الطلب' => 'request_number',
        'تاريخ الإنشاء' => 'creation_date',
        'منشئ الطلب' => 'created_by',
        'تاريخ التعديل' => 'modification_date',
        'معدل الطلب' => 'modified_by',
        'حالة الطلب' => 'request_status',
        'وصف الحالة' => 'status_description',
        'نوع الطلب' => 'request_type',
        'اسم نوع الطلب' => 'request_type_name',
        'رقم التسجيل الضريبي' => 'tax_registration_number',
        'كود المأمورية' => 'office_code',
        'اسم المأمورية' => 'office_name',
        'اسم المكلف' => 'taxpayer_name',
        'العنوان' => 'address',
        'رقم الهاتف' => 'phone_number',
        'البريد الإلكتروني' => 'email',
        'نوع المحاسبة' => 'accounting_type',
        'وصف نوع المحاسبة' => 'accounting_type_description',
        'الوعاء' => 'container',
        'اسم الوعاء' => 'container_name',
        'الفترة' => 'period',
        'مرحلة النزاع' => 'dispute_stage',
        'وصف مرحلة النزاع' => 'dispute_stage_description',
        'رقم القضية' => 'case_number',
        'جهة النزاع' => 'dispute_authority',
        'اسم جهة النزاع' => 'dispute_authority_name',
        'جهة أخرى' => 'other_authority',
        'الضريبة طبقاً للإقرار' => 'tax_according_to_declaration',
        'الضريبة من آخر ربط' => 'tax_from_last_assessment',
        'سنة آخر ربط' => 'last_assessment_year',
        'الضريبة طبقاً للنموذج' => 'tax_according_to_form',
        'الضريبة المسددة' => 'tax_paid',
        'الضريبة طبقاً للإقرار قبل التعديل' => 'tax_according_to_declaration_before_modification',
        'الضريبة من آخر ربط قبل التعديل' => 'tax_from_last_assessment_before_modification',
        'الضريبة طبقاً للنموذج قبل التعديل' => 'tax_according_to_form_before_modification',
        'الضريبة المسددة قبل التعديل' => 'tax_paid_before_modification',
        'الضريبة المتوقعة' => 'expected_tax',
        'الضريبة طبقاً للقانون' => 'tax_according_to_law',
        'الضريبة المستحقة' => 'tax_due_payment',
        'تاريخ انشاء الحالة' => 'status_creation_date',
        'تاريخ إنشاء الحالة' => 'status_creation_date'
    ];
}

// تحديد الحقول لكل جدول
function getTableFields() {
    $mapping = getColumnMapping();
    
    return [
        'accounting' => [
            'request_number', 'creation_date', 'status_creation_date', 'created_by', 'modification_date', 'modified_by',
            'request_status', 'status_description', 'request_type', 'request_type_name',
            'tax_registration_number', 'office_code', 'office_name', 'taxpayer_name',
            'address', 'phone_number', 'email', 'accounting_type', 'accounting_type_description',
            'container', 'container_name', 'period', 'expected_tax', 'tax_according_to_law'
        ],
        'conflict' => [
            'request_number', 'creation_date', 'status_creation_date', 'created_by', 'modification_date', 'modified_by',
            'request_status', 'status_description', 'request_type', 'request_type_name',
            'tax_registration_number', 'office_code', 'office_name', 'taxpayer_name',
            'address', 'phone_number', 'email', 'accounting_type', 'accounting_type_description',
            'container', 'container_name', 'period', 'dispute_stage', 'dispute_stage_description',
            'case_number', 'dispute_authority', 'dispute_authority_name', 'other_authority',
            'tax_according_to_declaration', 'tax_from_last_assessment', 'last_assessment_year',
            'tax_according_to_form', 'tax_paid', 'tax_according_to_declaration_before_modification',
            'tax_from_last_assessment_before_modification', 'tax_according_to_form_before_modification',
            'tax_paid_before_modification', 'expected_tax', 'tax_according_to_law', 'tax_due_payment'
        ],
        'dispute' => [
            'request_number', 'creation_date', 'status_creation_date', 'created_by', 'modification_date', 'modified_by',
            'request_status', 'status_description', 'request_type', 'request_type_name',
            'tax_registration_number', 'office_code', 'office_name', 'taxpayer_name',
            'address', 'phone_number', 'email', 'accounting_type', 'accounting_type_description',
            'container', 'container_name', 'period', 'dispute_stage', 'dispute_stage_description',
            'case_number', 'dispute_authority', 'dispute_authority_name', 'other_authority',
            'tax_according_to_declaration', 'tax_from_last_assessment', 'last_assessment_year',
            'tax_according_to_form', 'tax_paid', 'tax_according_to_declaration_before_modification',
            'tax_from_last_assessment_before_modification', 'tax_according_to_form_before_modification',
            'tax_paid_before_modification', 'expected_tax', 'tax_according_to_law', 'tax_due_payment'
        ]
    ];
}

// دالة لتحديد نوع البيانات
function getFieldDataType($fieldName) {
    if (strpos($fieldName, 'date') !== false) {
        return 'datetime';
    } elseif (strpos($fieldName, 'tax_') !== false || strpos($fieldName, 'expected_') !== false) {
        return 'decimal(15,2)';
    } elseif (in_array($fieldName, ['request_status', 'request_type', 'accounting_type', 'container', 'dispute_stage', 'dispute_authority'])) {
        return 'varchar(10)';
    } elseif (in_array($fieldName, ['office_code', 'last_assessment_year'])) {
        return 'varchar(10)';
    } elseif (in_array($fieldName, ['request_number', 'tax_registration_number', 'phone_number', 'case_number'])) {
        return 'varchar(50)';
    } elseif ($fieldName === 'address') {
        return 'text';
    } else {
        return 'varchar(255)';
    }
}

// دالة للحصول على التعليق العربي
function getFieldComment($fieldName) {
    $mapping = getColumnMapping();
    $reverseMapping = array_flip($mapping);
    
    if (isset($reverseMapping[$fieldName])) {
        return $reverseMapping[$fieldName] . ' | ' . ucwords(str_replace('_', ' ', $fieldName));
    }
    
    return ucwords(str_replace('_', ' ', $fieldName));
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة إنشاء الجداول بالتحويل الصحيح - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            cursor: pointer;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 0.9rem;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 0.9rem;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 إعادة إنشاء الجداول بالتحويل الصحيح</h1>
            <p>إنشاء الجداول الثلاثة باستخدام تحويل أسماء الأعمدة من debug_import.php</p>
        </div>
        
        <div class="content">
            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['recreate_tables'])) {
                try {
                    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    echo "<div class='section success'>";
                    echo "<h3>✅ تم الاتصال بقاعدة البيانات بنجاح</h3>";
                    echo "</div>";
                    
                    $tableFields = getTableFields();
                    $tablesCreated = [];
                    
                    foreach ($tableFields as $tableName => $fields) {
                        echo "<div class='section'>";
                        echo "<h3>🔄 إعادة إنشاء جدول $tableName</h3>";
                        
                        // حذف الجدول الموجود
                        $pdo->exec("DROP TABLE IF EXISTS `$tableName`");
                        echo "<p>🗑️ تم حذف الجدول الموجود</p>";
                        
                        // إنشاء SQL للجدول الجديد
                        $sql = "CREATE TABLE `$tableName` (\n";
                        
                        foreach ($fields as $field) {
                            $dataType = getFieldDataType($field);
                            $comment = getFieldComment($field);
                            $sql .= "    `$field` $dataType COMMENT '" . addslashes($comment) . "',\n";
                        }
                        
                        $sql .= "    `export_date` date NOT NULL COMMENT 'تاريخ التصدير | Export Date',\n";
                        $sql .= "    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,\n";
                        $sql .= "    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n";
                        $sql .= "    \n";
                        $sql .= "    KEY `idx_export_date` (`export_date`),\n";
                        $sql .= "    KEY `idx_request_number` (`request_number`),\n";
                        $sql .= "    KEY `idx_office_code` (`office_code`),\n";
                        $sql .= "    KEY `idx_taxpayer_name` (`taxpayer_name`),\n";
                        $sql .= "    KEY `idx_request_status` (`request_status`)\n";
                        $sql .= ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci\n";
                        $sql .= "COMMENT='جدول $tableName - " . count($fields) . " حقل + export_date';";
                        
                        // تنفيذ SQL
                        $pdo->exec($sql);
                        echo "<p>✅ تم إنشاء الجدول بنجاح</p>";
                        echo "<p>📊 عدد الحقول: " . count($fields) . " + export_date</p>";
                        
                        $tablesCreated[$tableName] = count($fields);
                        echo "</div>";
                    }
                    
                    // ملخص النتائج
                    echo "<div class='section success'>";
                    echo "<h3>🎉 تم إنشاء جميع الجداول بنجاح!</h3>";
                    echo "<ul>";
                    foreach ($tablesCreated as $table => $fieldCount) {
                        echo "<li>✅ جدول $table: $fieldCount حقل + export_date</li>";
                    }
                    echo "</ul>";
                    echo "<p>الجداول الآن جاهزة للاستيراد باستخدام تحويل أسماء الأعمدة من debug_import.php</p>";
                    echo "</div>";
                    
                } catch (PDOException $e) {
                    echo "<div class='section error'>";
                    echo "<h3>❌ خطأ في قاعدة البيانات</h3>";
                    echo "<p>" . $e->getMessage() . "</p>";
                    echo "</div>";
                }
            }
            ?>
            
            <!-- معلومات الجداول -->
            <div class="section">
                <h3>📊 تفاصيل الجداول المراد إنشاؤها</h3>
                
                <?php
                $tableFields = getTableFields();
                foreach ($tableFields as $tableName => $fields) {
                    echo "<h4>جدول $tableName (" . count($fields) . " حقل):</h4>";
                    echo "<table>";
                    echo "<tr><th>الرقم</th><th>اسم الحقل</th><th>نوع البيانات</th><th>التعليق</th></tr>";
                    
                    foreach ($fields as $index => $field) {
                        $dataType = getFieldDataType($field);
                        $comment = getFieldComment($field);
                        
                        echo "<tr>";
                        echo "<td>" . ($index + 1) . "</td>";
                        echo "<td>$field</td>";
                        echo "<td>$dataType</td>";
                        echo "<td>$comment</td>";
                        echo "</tr>";
                    }
                    
                    echo "</table>";
                }
                ?>
            </div>
            
            <!-- تحذير -->
            <div class="section warning">
                <h3>⚠️ تحذير مهم</h3>
                <p>هذه العملية ستقوم بـ:</p>
                <ul>
                    <li><strong>حذف الجداول الموجودة</strong> (accounting, conflict, dispute)</li>
                    <li><strong>إنشاء جداول جديدة</strong> بأسماء الحقول من debug_import.php</li>
                    <li><strong>فقدان جميع البيانات الموجودة</strong> في الجداول</li>
                </ul>
                <p>تأكد من عمل نسخة احتياطية قبل المتابعة!</p>
            </div>
            
            <!-- نموذج الإنشاء -->
            <div class="section">
                <h3>🔄 إعادة إنشاء الجداول</h3>
                <p>اضغط الزر أدناه لإعادة إنشاء الجداول الثلاثة بأسماء الحقول الصحيحة:</p>
                
                <form method="post">
                    <input type="hidden" name="recreate_tables" value="1">
                    <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من إعادة إنشاء الجداول؟ سيتم فقدان جميع البيانات الموجودة!')">
                        🔄 إعادة إنشاء الجداول
                    </button>
                </form>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="import_data.php" class="btn">استيراد البيانات</a>
                <a href="debug_import.php" class="btn">تشخيص الاستيراد</a>
            </div>
        </div>
    </div>
</body>
</html>
