<?php
$pageTitle = 'تحليل وتصميم قاعدة البيانات';
include 'header.php';
?>
<!-- تحليل وتصميم قاعدة بيانات مقترحة بناءً على تحليل الجداول -->
<!-- آخر تحديث: 17 يونيو 2025 -->
<!-- هذا الملف يوضح جداول قاعدة البيانات، الحقول، أنواع البيانات، المفاتيح الأساسية والأجنبية، وخطوات التنفيذ -->
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تصميم قاعدة البيانات المقترحة</title>
    <style>
        body { font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif; background: #f9f9f9; color: #222; margin: 0; padding: 0; }
        .container { max-width: 1000px; margin: 40px auto; background: #fff; border-radius: 10px; box-shadow: 0 2px 8px #ccc; padding: 32px; }
        h1, h2, h3 { color: #1a237e; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 32px; }
        th, td { border: 1px solid #bbb; padding: 8px 12px; text-align: right; }
        th { background: #e3e6f3; }
        .note { color: #555; font-size: 15px; margin-bottom: 20px; }
        ul { margin: 0 0 16px 0; padding-right: 20px; }
    </style>
</head>
<body>
<div class="container">
    <h1>تصميم قاعدة البيانات المقترحة</h1>
    <div class="note">تم بناء التصميم بناءً على تحليل الجداول والعلاقات المرجعية.</div>

    <h2>1. جدول التصنيف (classification)</h2>
    <table>
        <tr><th>اسم الحقل (عربي)</th><th>Field Name (EN)</th><th>النوع</th><th>مفتاح</th><th>الوصف</th></tr>
        <tr><td>كود المأمورية</td><td>office_code</td><td>INT</td><td>PRIMARY</td><td>مفتاح أساسي</td></tr>
        <tr><td>التصنيف</td><td>classification</td><td>VARCHAR(50)</td><td></td><td>نوع التصنيف</td></tr>
        <tr><td>اسم المأمورية</td><td>office_name</td><td>VARCHAR(100)</td><td></td><td>اسم المأمورية</td></tr>
        <tr><td>المنطقة</td><td>region</td><td>VARCHAR(100)</td><td></td><td>المنطقة الجغرافية</td></tr>
        <tr><td>المامورية</td><td>office</td><td>VARCHAR(100)</td><td></td><td>اسم مختصر للمأمورية</td></tr>
        <tr><td>الاختصاص</td><td>specialization</td><td>VARCHAR(100)</td><td></td><td>الاختصاص الإداري</td></tr>
        <tr><td>مديري العموم</td><td>general_managers</td><td>VARCHAR(100)</td><td></td><td>اسم المدير العام</td></tr>
    </table>

    <h2>2. جدول الوعاء (container)</h2>
    <table>
        <tr><th>اسم الحقل (عربي)</th><th>Field Name (EN)</th><th>النوع</th><th>مفتاح</th><th>الوصف</th></tr>
        <tr><td>الوعاء</td><td>container_id</td><td>INT</td><td>PRIMARY</td><td>مفتاح أساسي</td></tr>
        <tr><td>اسم الوعاء</td><td>container_name</td><td>VARCHAR(100)</td><td></td><td>اسم الوعاء</td></tr>
        <tr><td>التصنيف</td><td>classification</td><td>VARCHAR(50)</td><td>FOREIGN</td><td>مفتاح أجنبي من جدول التصنيف</td></tr>
    </table>

    <h2>3. جدول حالة الطلب (request_status)</h2>
    <table>
        <tr><th>اسم الحقل (عربي)</th><th>Field Name (EN)</th><th>النوع</th><th>مفتاح</th><th>الوصف</th></tr>
        <tr><td>حالة الطلب</td><td>status_id</td><td>INT</td><td>PRIMARY</td><td>مفتاح أساسي</td></tr>
        <tr><td>وصف الحالة</td><td>status_description</td><td>VARCHAR(100)</td><td></td><td>وصف نصي للحالة</td></tr>
    </table>

    <h2>4. جدول الطلبات (requests)</h2>
    <table>
        <tr><th>اسم الحقل (عربي)</th><th>Field Name (EN)</th><th>النوع</th><th>مفتاح</th><th>الوصف</th></tr>
        <tr><td>رقم الطلب</td><td>request_id</td><td>BIGINT</td><td>PRIMARY</td><td>مفتاح أساسي</td></tr>
        <tr><td>تاريخ انشاء الحالة</td><td>status_created_at</td><td>DATETIME</td><td></td><td>تاريخ ووقت الإنشاء</td></tr>
        <tr><td>منشأ بواسطة</td><td>created_by</td><td>VARCHAR(50)</td><td></td><td>اسم المستخدم</td></tr>
        <tr><td>تم التعديل بواسطة</td><td>modified_by</td><td>VARCHAR(50)</td><td></td><td>اسم المستخدم</td></tr>
        <tr><td>تاريخ التعديل</td><td>modified_at</td><td>DATETIME</td><td></td><td>تاريخ ووقت التعديل</td></tr>
        <tr><td>الحالة</td><td>status_id</td><td>INT</td><td>FOREIGN</td><td>مفتاح أجنبي من جدول حالة الطلب</td></tr>
        <tr><td>رقم التسجيل الضريبي</td><td>tax_registration_number</td><td>BIGINT</td><td></td><td>رقم التسجيل الضريبي</td></tr>
        <tr><td>كود المأمورية</td><td>office_code</td><td>INT</td><td>FOREIGN</td><td>مفتاح أجنبي من جدول التصنيف</td></tr>
        <tr><td>اسم المأمورية</td><td>office_name</td><td>VARCHAR(100)</td><td></td><td>اسم المأمورية</td></tr>
        <tr><td>اسم الممول</td><td>taxpayer_name</td><td>VARCHAR(100)</td><td></td><td>اسم الممول</td></tr>
        <tr><td>العنوان</td><td>address</td><td>VARCHAR(200)</td><td></td><td>عنوان الممول</td></tr>
        <tr><td>البريد الالكتروني</td><td>email</td><td>VARCHAR(100)</td><td></td><td>البريد الإلكتروني</td></tr>
        <tr><td>رقم التليفون</td><td>phone</td><td>VARCHAR(20)</td><td></td><td>رقم الهاتف</td></tr>
        <tr><td>الوعاء</td><td>container_id</td><td>INT</td><td>FOREIGN</td><td>مفتاح أجنبي من جدول الوعاء</td></tr>
        <tr><td>اسم الوعاء</td><td>container_name</td><td>VARCHAR(100)</td><td></td><td>اسم الوعاء</td></tr>
        <tr><td>الفترة</td><td>period</td><td>VARCHAR(20)</td><td></td><td>سنة أو فترة الطلب</td></tr>
        <tr><td>قيمة الوعاء</td><td>container_value</td><td>DECIMAL(18,2)</td><td></td><td>قيمة الوعاء</td></tr>
        <tr><td>تكلفة اقتناء الاسهم</td><td>shares_cost</td><td>DECIMAL(18,2)</td><td></td><td>تكلفة اقتناء الأسهم</td></tr>
        <tr><td>قيمة الوعاء قبل التعديل</td><td>container_value_before</td><td>DECIMAL(18,2)</td><td></td><td>قيمة الوعاء قبل التعديل</td></tr>
        <tr><td>تكلفة اقتناء الاسهم قبل التعديل</td><td>shares_cost_before</td><td>DECIMAL(18,2)</td><td></td><td>تكلفة اقتناء الأسهم قبل التعديل</td></tr>
        <tr><td>الضريبة المتوقعة</td><td>expected_tax</td><td>DECIMAL(18,2)</td><td></td><td>الضريبة المتوقعة</td></tr>
        <tr><td>الضريبة طبقاً للقانون</td><td>tax_by_law</td><td>DECIMAL(18,2)</td><td></td><td>الضريبة حسب القانون</td></tr>
        <!-- أضف باقي الحقول حسب الحاجة -->
    </table>

    <h2>تصميم قاعدة البيانات بناءً على جميع جداول Main Table وربطها بجداول Mapping Table</h2>
    <div class="note">تم بناء التصميم بناءً على جميع جداول Main Table وربطها بجداول Mapping Table المرجعية.</div>

    <h3>1. جدول التصنيف (classification)</h3>
    <table>
        <tr><th>اسم الحقل (عربي)</th><th>Field Name (EN)</th><th>النوع</th><th>مفتاح</th><th>الوصف</th></tr>
        <tr><td>كود المأمورية</td><td>office_code</td><td>INT</td><td>PRIMARY</td><td>مفتاح أساسي</td></tr>
        <tr><td>التصنيف</td><td>classification</td><td>VARCHAR(50)</td><td></td><td>نوع التصنيف</td></tr>
        <tr><td>اسم المأمورية</td><td>office_name</td><td>VARCHAR(100)</td><td></td><td>اسم المأمورية</td></tr>
        <tr><td>المنطقة</td><td>region</td><td>VARCHAR(100)</td><td></td><td>المنطقة الجغرافية</td></tr>
        <tr><td>المامورية</td><td>office</td><td>VARCHAR(100)</td><td></td><td>اسم مختصر للمأمورية</td></tr>
        <tr><td>الاختصاص</td><td>specialization</td><td>VARCHAR(100)</td><td></td><td>الاختصاص الإداري</td></tr>
        <tr><td>مديري العموم</td><td>general_managers</td><td>VARCHAR(100)</td><td></td><td>اسم المدير العام</td></tr>
    </table>

    <h3>2. جدول الوعاء (container)</h3>
    <table>
        <tr><th>اسم الحقل (عربي)</th><th>Field Name (EN)</th><th>النوع</th><th>مفتاح</th><th>الوصف</th></tr>
        <tr><td>الوعاء</td><td>container_id</td><td>INT</td><td>PRIMARY</td><td>مفتاح أساسي</td></tr>
        <tr><td>اسم الوعاء</td><td>container_name</td><td>VARCHAR(100)</td><td></td><td>اسم الوعاء</td></tr>
        <tr><td>التصنيف</td><td>classification</td><td>VARCHAR(50)</td><td>FOREIGN</td><td>مفتاح أجنبي من جدول التصنيف</td></tr>
    </table>

    <h3>3. جدول حالة الطلب (request_status)</h3>
    <table>
        <tr><th>اسم الحقل (عربي)</th><th>Field Name (EN)</th><th>النوع</th><th>مفتاح</th><th>الوصف</th></tr>
        <tr><td>حالة الطلب</td><td>status_id</td><td>INT</td><td>PRIMARY</td><td>مفتاح أساسي</td></tr>
        <tr><td>وصف الحالة</td><td>status_description</td><td>VARCHAR(100)</td><td></td><td>وصف نصي للحالة</td></tr>
    </table>

    <h3>4. جدول الطلبات (requests)</h3>
    <table>
        <tr><th>اسم الحقل (عربي)</th><th>Field Name (EN)</th><th>النوع</th><th>مفتاح</th><th>الوصف</th></tr>
        <tr><td>رقم الطلب</td><td>request_id</td><td>BIGINT</td><td>PRIMARY</td><td>مفتاح أساسي</td></tr>
        <tr><td>تاريخ انشاء الطلب</td><td>request_created_at</td><td>DATETIME</td><td></td><td>تاريخ ووقت الإنشاء</td></tr>
        <tr><td>منشأ بواسطة</td><td>created_by</td><td>VARCHAR(50)</td><td></td><td>اسم المستخدم</td></tr>
        <tr><td>تم التعديل بواسطة</td><td>modified_by</td><td>VARCHAR(50)</td><td></td><td>اسم المستخدم</td></tr>
        <tr><td>تاريخ التعديل</td><td>modified_at</td><td>DATETIME</td><td></td><td>تاريخ ووقت التعديل</td></tr>
        <tr><td>الحالة</td><td>status_id</td><td>INT</td><td>FOREIGN</td><td>مفتاح أجنبي من جدول حالة الطلب</td></tr>
        <tr><td>وصف الحالة</td><td>status_description</td><td>VARCHAR(100)</td><td></td><td>وصف نصي للحالة</td></tr>
        <tr><td>نوع الطلب</td><td>request_type</td><td>VARCHAR(50)</td><td></td><td>نوع الطلب (حسابات/نزاع/تسوية...)</td></tr>
        <tr><td>اسم نوع الطلب</td><td>request_type_name</td><td>VARCHAR(100)</td><td></td><td>اسم نوع الطلب</td></tr>
        <tr><td>رقم التسجيل الضريبي</td><td>tax_registration_number</td><td>BIGINT</td><td></td><td>رقم التسجيل الضريبي</td></tr>
        <tr><td>كود المأمورية</td><td>office_code</td><td>INT</td><td>FOREIGN</td><td>مفتاح أجنبي من جدول التصنيف</td></tr>
        <tr><td>اسم المأمورية</td><td>office_name</td><td>VARCHAR(100)</td><td></td><td>اسم المأمورية</td></tr>
        <tr><td>اسم الممول</td><td>taxpayer_name</td><td>VARCHAR(100)</td><td></td><td>اسم الممول</td></tr>
        <tr><td>العنوان</td><td>address</td><td>VARCHAR(200)</td><td></td><td>عنوان الممول</td></tr>
        <tr><td>البريد الالكتروني</td><td>email</td><td>VARCHAR(100)</td><td></td><td>البريد الإلكتروني</td></tr>
        <tr><td>رقم التليفون</td><td>phone</td><td>VARCHAR(20)</td><td></td><td>رقم الهاتف</td></tr>
        <tr><td>الوعاء</td><td>container_id</td><td>INT</td><td>FOREIGN</td><td>مفتاح أجنبي من جدول الوعاء</td></tr>
        <tr><td>اسم الوعاء</td><td>container_name</td><td>VARCHAR(100)</td><td></td><td>اسم الوعاء</td></tr>
        <tr><td>الفترة</td><td>period</td><td>VARCHAR(20)</td><td></td><td>سنة أو فترة الطلب</td></tr>
        <tr><td>قيمة الوعاء</td><td>container_value</td><td>DECIMAL(18,2)</td><td></td><td>قيمة الوعاء</td></tr>
        <tr><td>تكلفة اقتناء الاسهم</td><td>shares_cost</td><td>DECIMAL(18,2)</td><td></td><td>تكلفة اقتناء الأسهم</td></tr>
        <tr><td>قيمة الوعاء قبل التعديل</td><td>container_value_before</td><td>DECIMAL(18,2)</td><td></td><td>قيمة الوعاء قبل التعديل</td></tr>
        <tr><td>تكلفة اقتناء الاسهم قبل التعديل</td><td>shares_cost_before</td><td>DECIMAL(18,2)</td><td></td><td>تكلفة اقتناء الأسهم قبل التعديل</td></tr>
        <tr><td>الضريبة المتوقعة</td><td>expected_tax</td><td>DECIMAL(18,2)</td><td></td><td>الضريبة المتوقعة</td></tr>
        <tr><td>الضريبة طبقاً للقانون</td><td>tax_by_law</td><td>DECIMAL(18,2)</td><td></td><td>الضريبة حسب القانون</td></tr>
        <!-- أضف باقي الحقول من ملفات النزاع والتسوية حسب الحاجة -->
        <tr><td>مرحلة النزاع</td><td>dispute_stage</td><td>VARCHAR(50)</td><td></td><td>مرحلة النزاع (إن وجدت)</td></tr>
        <tr><td>وصف مرحلة النزاع</td><td>dispute_stage_name</td><td>VARCHAR(100)</td><td></td><td>وصف مرحلة النزاع</td></tr>
        <tr><td>رقم الدعوى/الطعن</td><td>case_number</td><td>VARCHAR(50)</td><td></td><td>رقم الدعوى أو الطعن</td></tr>
        <tr><td>جهة نظر النزاع</td><td>dispute_entity</td><td>VARCHAR(50)</td><td></td><td>جهة نظر النزاع</td></tr>
        <tr><td>اسم جهة نظر النزاع</td><td>dispute_entity_name</td><td>VARCHAR(100)</td><td></td><td>اسم جهة نظر النزاع</td></tr>
        <tr><td>جهة نظر أخرى</td><td>other_dispute_entity</td><td>VARCHAR(100)</td><td></td><td>جهة نظر أخرى</td></tr>
        <tr><td>الضريبة طبقاً للإقرار</td><td>tax_by_declaration</td><td>DECIMAL(18,2)</td><td></td><td>الضريبة طبقاً للإقرار</td></tr>
        <tr><td>الضريبة من آخر ربط</td><td>tax_from_last_assessment</td><td>DECIMAL(18,2)</td><td></td><td>الضريبة من آخر ربط</td></tr>
        <tr><td>سنة آخر ربط</td><td>last_assessment_year</td><td>VARCHAR(20)</td><td></td><td>سنة آخر ربط</td></tr>
        <tr><td>الضريبة طبقاً للنموذج</td><td>tax_by_form</td><td>DECIMAL(18,2)</td><td></td><td>الضريبة طبقاً للنموذج</td></tr>
        <tr><td>الضريبة المسددة</td><td>tax_paid</td><td>DECIMAL(18,2)</td><td></td><td>الضريبة المسددة</td></tr>
        <tr><td>الضريبة المستحقة السداد</td><td>tax_due</td><td>DECIMAL(18,2)</td><td></td><td>الضريبة المستحقة السداد</td></tr>
    </table>

    <h3>خطوات تنفيذ قاعدة البيانات</h3>
    <ol>
        <li>إنشاء قاعدة البيانات (مثلاً: <b>tiaf_db</b>).</li>
        <li>إنشاء الجداول المرجعية أولاً (classification, container, request_status).</li>
        <li>إنشاء جدول الطلبات (requests) مع جميع الحقول المطلوبة.</li>
        <li>تحديد المفاتيح الأساسية (PRIMARY KEY) لكل جدول.</li>
        <li>تحديد المفاتيح الأجنبية (FOREIGN KEY) للعلاقات المرجعية بين الجداول.</li>
        <li>تعبئة الجداول المرجعية بالبيانات الثابتة أولاً.</li>
        <li>إدخال بيانات المعاملات في جدول الطلبات (requests) مع مراعاة التكامل المرجعي.</li>
        <li>إنشاء الفهارس (Indexes) على الحقول المستخدمة في البحث بكثرة (مثل: office_code, status_id, container_id).</li>
        <li>تحديث التصميم حسب الحاجة عند إضافة متطلبات جديدة أو جداول إضافية.</li>
    </ol>
    <div class="note">هذا التصميم يغطي جميع الحقول الأساسية من ملفات CSV في Main Table مع الربط الكامل بالجداول المرجعية من Mapping Table. يمكن التوسع مستقبلاً حسب متطلبات النظام.</div>
</div>
</body>
</html>
<?php include 'footer.php'; ?>
