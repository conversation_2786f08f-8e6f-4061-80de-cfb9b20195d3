<?php
/**
 * AJAX Handler للفلاتر المترابطة - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 2.0
 * @date 2025-06-17
 */

header('Content-Type: application/json; charset=utf-8');

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $action = $_GET['action'] ?? '';
    $response = [];
    
    switch ($action) {
        case 'get_managers':
            $classification_type = $_GET['classification_type'] ?? '';
            $sql = "SELECT DISTINCT general_manager FROM classification WHERE general_manager IS NOT NULL";
            if (!empty($classification_type)) {
                $sql .= " AND classification_type = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$classification_type]);
            } else {
                $stmt = $pdo->query($sql);
            }
            $sql .= " ORDER BY general_manager";
            $response = $stmt->fetchAll(PDO::FETCH_ASSOC);
            break;
            
        case 'get_specializations':
            $classification_type = $_GET['classification_type'] ?? '';
            $general_manager = $_GET['general_manager'] ?? '';
            
            $sql = "SELECT DISTINCT specialization FROM classification WHERE specialization IS NOT NULL";
            $params = [];
            
            if (!empty($classification_type)) {
                $sql .= " AND classification_type = ?";
                $params[] = $classification_type;
            }
            if (!empty($general_manager)) {
                $sql .= " AND general_manager = ?";
                $params[] = $general_manager;
            }
            
            $sql .= " ORDER BY specialization";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $response = $stmt->fetchAll(PDO::FETCH_ASSOC);
            break;
            
        case 'get_regions':
            $classification_type = $_GET['classification_type'] ?? '';
            $general_manager = $_GET['general_manager'] ?? '';
            $specialization = $_GET['specialization'] ?? '';
            
            $sql = "SELECT DISTINCT region FROM classification WHERE region IS NOT NULL";
            $params = [];
            
            if (!empty($classification_type)) {
                $sql .= " AND classification_type = ?";
                $params[] = $classification_type;
            }
            if (!empty($general_manager)) {
                $sql .= " AND general_manager = ?";
                $params[] = $general_manager;
            }
            if (!empty($specialization)) {
                $sql .= " AND specialization = ?";
                $params[] = $specialization;
            }
            
            $sql .= " ORDER BY region";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $response = $stmt->fetchAll(PDO::FETCH_ASSOC);
            break;
            
        case 'get_offices':
            $classification_type = $_GET['classification_type'] ?? '';
            $general_manager = $_GET['general_manager'] ?? '';
            $specialization = $_GET['specialization'] ?? '';
            $region = $_GET['region'] ?? '';
            
            $sql = "SELECT DISTINCT office_code, office_name FROM classification WHERE office_code IS NOT NULL";
            $params = [];
            
            if (!empty($classification_type)) {
                $sql .= " AND classification_type = ?";
                $params[] = $classification_type;
            }
            if (!empty($general_manager)) {
                $sql .= " AND general_manager = ?";
                $params[] = $general_manager;
            }
            if (!empty($specialization)) {
                $sql .= " AND specialization = ?";
                $params[] = $specialization;
            }
            if (!empty($region)) {
                $sql .= " AND region = ?";
                $params[] = $region;
            }
            
            $sql .= " ORDER BY office_name";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $response = $stmt->fetchAll(PDO::FETCH_ASSOC);
            break;
            
        default:
            $response = ['error' => 'Invalid action'];
            break;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
}
?>
