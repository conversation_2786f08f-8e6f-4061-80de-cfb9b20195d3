<?php
/**
 * إعادة إنشاء الجداول بأسماء الحقول من ملفات CSV - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.4.4
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

// دالة لإنشاء اسم الحقل من اسم العمود
function createFieldName($columnName) {
    // إزالة الرموز الخاصة والمسافات
    $fieldName = $columnName;
    $fieldName = str_replace([' ', '-', '/', '\\', '(', ')', '[', ']', '.', ',', ':', ';'], '_', $fieldName);
    $fieldName = preg_replace('/[^\p{L}\p{N}_]/u', '_', $fieldName);
    $fieldName = preg_replace('/_+/', '_', $fieldName);
    $fieldName = trim($fieldName, '_');
    $fieldName = strtolower($fieldName);
    
    // إذا كان الاسم فارغ أو يبدأ برقم، أضف بادئة
    if (empty($fieldName) || is_numeric($fieldName[0])) {
        $fieldName = 'field_' . $fieldName;
    }
    
    return $fieldName;
}

// دالة لتحديد نوع البيانات
function getDataType($columnName) {
    $columnLower = strtolower($columnName);
    
    if (strpos($columnLower, 'تاريخ') !== false || strpos($columnLower, 'date') !== false) {
        return 'datetime';
    } elseif (strpos($columnLower, 'ضريبة') !== false || strpos($columnLower, 'tax') !== false || 
             strpos($columnLower, 'مبلغ') !== false || strpos($columnLower, 'amount') !== false) {
        return 'decimal(15,2)';
    } elseif (strpos($columnLower, 'رقم') !== false && strpos($columnLower, 'هاتف') === false) {
        return 'varchar(50)';
    } elseif (strpos($columnLower, 'كود') !== false || strpos($columnLower, 'code') !== false) {
        return 'varchar(10)';
    } elseif (strpos($columnLower, 'عنوان') !== false || strpos($columnLower, 'address') !== false) {
        return 'text';
    } elseif (strpos($columnLower, 'وصف') !== false || strpos($columnLower, 'description') !== false) {
        return 'varchar(255)';
    } else {
        return 'varchar(255)';
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة إنشاء الجداول من CSV - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
        }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 0.9rem;
            white-space: pre-wrap;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 0.9rem;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 إعادة إنشاء الجداول من CSV</h1>
            <p>إنشاء جداول قاعدة البيانات بأسماء الحقول المطابقة لأعمدة CSV</p>
        </div>
        
        <div class="content">
            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_files'])) {
                try {
                    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    echo "<div class='section success'>";
                    echo "<h3>✅ تم الاتصال بقاعدة البيانات بنجاح</h3>";
                    echo "</div>";
                    
                    $files = $_FILES['csv_files'];
                    $tablesCreated = [];
                    
                    // معالجة كل ملف
                    for ($i = 0; $i < count($files['name']); $i++) {
                        if ($files['error'][$i] === UPLOAD_ERR_OK) {
                            $fileName = $files['name'][$i];
                            $tmpName = $files['tmp_name'][$i];
                            
                            // تحديد نوع الجدول من اسم الملف
                            $tableName = 'unknown';
                            if (strpos($fileName, 'Accounting') !== false) {
                                $tableName = 'accounting';
                            } elseif (strpos($fileName, 'Conflict') !== false) {
                                $tableName = 'conflict';
                            } elseif (strpos($fileName, 'Dispute') !== false) {
                                $tableName = 'dispute';
                            }
                            
                            if ($tableName === 'unknown') {
                                echo "<div class='section error'>";
                                echo "<h4>❌ لا يمكن تحديد نوع الجدول من اسم الملف: $fileName</h4>";
                                echo "</div>";
                                continue;
                            }
                            
                            echo "<div class='section'>";
                            echo "<h3>📁 معالجة ملف: " . htmlspecialchars($fileName) . "</h3>";
                            
                            // قراءة العناوين
                            $handle = fopen($tmpName, 'r');
                            $headers = fgetcsv($handle);
                            fclose($handle);
                            
                            // تنظيف العناوين
                            $cleanHeaders = array_map(function($header) {
                                return trim(str_replace("\xEF\xBB\xBF", '', $header));
                            }, $headers);
                            
                            echo "<p><strong>نوع الجدول:</strong> $tableName</p>";
                            echo "<p><strong>عدد الأعمدة:</strong> " . count($cleanHeaders) . "</p>";
                            
                            // حذف الجدول الموجود
                            $pdo->exec("DROP TABLE IF EXISTS `$tableName`");
                            echo "<p>🗑️ تم حذف الجدول الموجود</p>";
                            
                            // إنشاء SQL للجدول الجديد
                            $sql = "CREATE TABLE `$tableName` (\n";
                            
                            $fieldMappings = [];
                            foreach ($cleanHeaders as $header) {
                                $fieldName = createFieldName($header);
                                $dataType = getDataType($header);
                                $sql .= "    `$fieldName` $dataType COMMENT '" . addslashes($header) . "',\n";
                                $fieldMappings[$header] = $fieldName;
                            }
                            
                            $sql .= "    `export_date` date NOT NULL COMMENT 'تاريخ التصدير',\n";
                            $sql .= "    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,\n";
                            $sql .= "    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n";
                            $sql .= "    \n";
                            $sql .= "    KEY `idx_export_date` (`export_date`)\n";
                            $sql .= ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci\n";
                            $sql .= "COMMENT='جدول $tableName - " . count($cleanHeaders) . " حقل + export_date';";
                            
                            // تنفيذ SQL
                            $pdo->exec($sql);
                            echo "<p>✅ تم إنشاء الجدول بنجاح</p>";
                            
                            // عرض تفاصيل الحقول
                            echo "<h4>📋 تفاصيل الحقول:</h4>";
                            echo "<table>";
                            echo "<tr><th>الرقم</th><th>اسم العمود (CSV)</th><th>اسم الحقل (قاعدة البيانات)</th><th>نوع البيانات</th></tr>";
                            
                            foreach ($cleanHeaders as $index => $header) {
                                $fieldName = createFieldName($header);
                                $dataType = getDataType($header);
                                
                                echo "<tr>";
                                echo "<td>" . ($index + 1) . "</td>";
                                echo "<td>" . htmlspecialchars($header) . "</td>";
                                echo "<td>" . htmlspecialchars($fieldName) . "</td>";
                                echo "<td>" . htmlspecialchars($dataType) . "</td>";
                                echo "</tr>";
                            }
                            
                            echo "</table>";
                            
                            $tablesCreated[$tableName] = [
                                'columns' => count($cleanHeaders),
                                'mappings' => $fieldMappings
                            ];
                            
                            echo "</div>";
                        }
                    }
                    
                    // ملخص النتائج
                    if (!empty($tablesCreated)) {
                        echo "<div class='section success'>";
                        echo "<h3>🎉 تم إنشاء الجداول بنجاح!</h3>";
                        echo "<ul>";
                        foreach ($tablesCreated as $table => $info) {
                            echo "<li>✅ جدول $table: {$info['columns']} حقل + export_date</li>";
                        }
                        echo "</ul>";
                        
                        // إنشاء دالة تحويل أسماء الأعمدة
                        echo "<h4>🔄 دالة تحويل أسماء الأعمدة (للاستخدام في import_data.php):</h4>";
                        echo "<pre>";
                        echo "function getColumnMapping() {\n";
                        echo "    return [\n";
                        
                        foreach ($tablesCreated as $table => $info) {
                            echo "        // أعمدة جدول $table\n";
                            foreach ($info['mappings'] as $csvColumn => $dbField) {
                                echo "        '" . addslashes($csvColumn) . "' => '$dbField',\n";
                            }
                            echo "\n";
                        }
                        
                        echo "    ];\n";
                        echo "}";
                        echo "</pre>";
                        
                        echo "</div>";
                    }
                    
                } catch (PDOException $e) {
                    echo "<div class='section error'>";
                    echo "<h3>❌ خطأ في قاعدة البيانات</h3>";
                    echo "<p>" . $e->getMessage() . "</p>";
                    echo "</div>";
                }
            }
            ?>
            
            <!-- نموذج رفع الملفات -->
            <div class="section">
                <h3>📤 رفع ملفات CSV لإنشاء الجداول</h3>
                <p>ارفع ملفات AccountingEXPORT، ConflictEXPORT، و DisputeEXPORT لإنشاء الجداول بأسماء الحقول الصحيحة:</p>
                
                <form method="post" enctype="multipart/form-data">
                    <div class="upload-area">
                        <p>📁 اختر ملفات CSV (يمكن اختيار عدة ملفات)</p>
                        <input type="file" name="csv_files[]" accept=".csv" multiple required>
                        <p><small>اختر ملفات: AccountingEXPORT_*.csv, ConflictEXPORT_*.csv, DisputeEXPORT_*.csv</small></p>
                    </div>
                    <button type="submit" class="btn">🔄 إنشاء الجداول</button>
                </form>
            </div>
            
            <!-- معلومات مفيدة -->
            <div class="section warning">
                <h3>⚠️ تحذير مهم</h3>
                <p>هذه العملية ستقوم بـ:</p>
                <ul>
                    <li><strong>حذف الجداول الموجودة</strong> (accounting, conflict, dispute)</li>
                    <li><strong>إنشاء جداول جديدة</strong> بأسماء حقول مطابقة لأعمدة CSV</li>
                    <li><strong>فقدان جميع البيانات الموجودة</strong> في الجداول</li>
                </ul>
                <p>تأكد من عمل نسخة احتياطية قبل المتابعة!</p>
            </div>
            
            <div class="section">
                <h3>💡 كيفية الاستخدام</h3>
                <ol>
                    <li><strong>ارفع ملفات CSV الأصلية</strong> التي تريد استيراد البيانات منها</li>
                    <li><strong>سيتم إنشاء الجداول</strong> بأسماء حقول مطابقة تماماً لأعمدة CSV</li>
                    <li><strong>انسخ دالة تحويل الأسماء</strong> وضعها في ملف import_data.php</li>
                    <li><strong>قم بالاستيراد</strong> باستخدام الجداول الجديدة</li>
                </ol>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="import_data.php" class="btn">استيراد البيانات</a>
                <a href="extract_column_names.php" class="btn">استخراج أسماء الأعمدة</a>
            </div>
        </div>
    </div>
</body>
</html>
