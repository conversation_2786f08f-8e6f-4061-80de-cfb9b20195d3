<?php
/**
 * ملف إعداد قاعدة البيانات - الجزء الثاني
 * إنشاء الجداول الأساسية (Accounting, Conflict, Dispute)
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🚀 إنشاء الجداول الأساسية - TIaF Report System</h2>";
    
    // ===== إنشاء جدول طلبات المحاسبة (Accounting) =====
    // بدون قيود أو مفاتيح منفردة لتسهيل الاستيراد المستمر
    $sql_accounting = "
    CREATE TABLE IF NOT EXISTS `accounting` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT,
        `request_number` varchar(50),
        `creation_date` datetime,
        `created_by` varchar(100),
        `modification_date` datetime,
        `modified_by` varchar(100),
        `request_status` varchar(10),
        `status_description` varchar(255),
        `request_type` varchar(10),
        `request_type_name` varchar(255),
        `tax_registration_number` varchar(50),
        `office_code` varchar(10),
        `office_name` varchar(255),
        `taxpayer_name` varchar(255),
        `address` text,
        `phone_number` varchar(50),
        `email` varchar(255),
        `accounting_type` varchar(10),
        `accounting_type_description` varchar(255),
        `container` varchar(10),
        `container_name` varchar(255),
        `period` varchar(20),
        `dispute_stage` varchar(10),
        `dispute_stage_description` varchar(255),
        `case_number` varchar(100),
        `dispute_authority` varchar(10),
        `dispute_authority_name` varchar(255),
        `other_authority` varchar(255),
        `tax_according_to_declaration` decimal(15,2),
        `tax_from_last_assessment` decimal(15,2),
        `last_assessment_year` varchar(10),
        `tax_according_to_form` decimal(15,2),
        `tax_paid` decimal(15,2),
        `tax_according_to_declaration_before_modification` decimal(15,2),
        `tax_from_last_assessment_before_modification` decimal(15,2),
        `tax_according_to_form_before_modification` decimal(15,2),
        `tax_paid_before_modification` decimal(15,2),
        `expected_tax` decimal(15,2),
        `tax_according_to_law` decimal(15,2),
        `tax_due_payment` decimal(15,2),
        `export_date` date,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_accounting);
    echo "<p>✅ تم إنشاء جدول طلبات المحاسبة (accounting)</p>";
    
    // ===== إنشاء جدول طلبات إنهاء النزاع (Conflict) =====
    // بدون قيود أو مفاتيح منفردة لتسهيل الاستيراد المستمر
    $sql_conflict = "
    CREATE TABLE IF NOT EXISTS `conflict` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT,
        `request_number` varchar(50),
        `creation_date` datetime,
        `created_by` varchar(100),
        `modification_date` datetime,
        `modified_by` varchar(100),
        `request_status` varchar(10),
        `status_description` varchar(255),
        `request_type` varchar(10),
        `request_type_name` varchar(255),
        `tax_registration_number` varchar(50),
        `office_code` varchar(10),
        `office_name` varchar(255),
        `taxpayer_name` varchar(255),
        `address` text,
        `phone_number` varchar(50),
        `email` varchar(255),
        `accounting_type` varchar(10),
        `accounting_type_description` varchar(255),
        `container` varchar(10),
        `container_name` varchar(255),
        `period` varchar(20),
        `dispute_stage` varchar(10),
        `dispute_stage_description` varchar(255),
        `case_number` varchar(100),
        `dispute_authority` varchar(10),
        `dispute_authority_name` varchar(255),
        `other_authority` varchar(255),
        `tax_according_to_declaration` decimal(15,2),
        `tax_from_last_assessment` decimal(15,2),
        `last_assessment_year` varchar(10),
        `tax_according_to_form` decimal(15,2),
        `tax_paid` decimal(15,2),
        `tax_according_to_declaration_before_modification` decimal(15,2),
        `tax_from_last_assessment_before_modification` decimal(15,2),
        `tax_according_to_form_before_modification` decimal(15,2),
        `tax_paid_before_modification` decimal(15,2),
        `expected_tax` decimal(15,2),
        `tax_according_to_law` decimal(15,2),
        `tax_due_payment` decimal(15,2),
        `export_date` date,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_conflict);
    echo "<p>✅ تم إنشاء جدول طلبات إنهاء النزاع (conflict)</p>";
    
    echo "<h3>تم إنشاء الجداول الأساسية بنجاح!</h3>";
    echo "<p><a href='database_setup_part3.php'>انقر هنا لإكمال إنشاء جدول طلبات تسوية النزاع</a></p>";
    echo "<p><a href='index.php'>العودة للصفحة الرئيسية</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    die();
}
?>
