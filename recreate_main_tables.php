<?php
/**
 * إعادة إنشاء الجداول الأساسية بدون مفتاح أساسي - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.3
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔄 إعادة إنشاء الجداول الأساسية - TIaF Report System</h2>";
    echo "<p><strong>ملاحظة:</strong> سيتم حذف الجداول الأساسية الحالية وإعادة إنشاؤها بدون مفتاح أساسي id</p>";
    echo "<p><strong>البيانات منظمة حسب export_date المستخرج من اسم الملف</strong></p>";
    
    // ===== حذف الجداول الأساسية الحالية =====
    echo "<h3>🗑️ حذف الجداول الأساسية الحالية</h3>";
    
    $tables_to_drop = ['accounting', 'conflict', 'dispute'];
    foreach ($tables_to_drop as $table) {
        try {
            $pdo->exec("DROP TABLE IF EXISTS `$table`");
            echo "<p>✅ تم حذف جدول $table</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ تحذير: لم يتم العثور على جدول $table</p>";
        }
    }
    
    // ===== إعادة إنشاء الجداول الأساسية =====
    echo "<h3>🏗️ إعادة إنشاء الجداول الأساسية</h3>";
    
    // جدول طلبات المحاسبة - بدون مفتاح أساسي id
    $sql_accounting = "
    CREATE TABLE `accounting` (
        `request_number` varchar(50) COMMENT 'رقم الطلب',
        `creation_date` datetime COMMENT 'تاريخ الإنشاء',
        `created_by` varchar(100) COMMENT 'منشئ الطلب',
        `modification_date` datetime COMMENT 'تاريخ التعديل',
        `modified_by` varchar(100) COMMENT 'معدل الطلب',
        `request_status` varchar(10) COMMENT 'حالة الطلب',
        `status_description` varchar(255) COMMENT 'وصف الحالة',
        `request_type` varchar(10) COMMENT 'نوع الطلب',
        `request_type_name` varchar(255) COMMENT 'اسم نوع الطلب',
        `tax_registration_number` varchar(50) COMMENT 'رقم التسجيل الضريبي',
        `office_code` varchar(10) COMMENT 'كود المأمورية',
        `office_name` varchar(255) COMMENT 'اسم المأمورية',
        `taxpayer_name` varchar(255) COMMENT 'اسم المكلف',
        `address` text COMMENT 'العنوان',
        `phone_number` varchar(50) COMMENT 'رقم الهاتف',
        `email` varchar(255) COMMENT 'البريد الإلكتروني',
        `accounting_type` varchar(10) COMMENT 'نوع المحاسبة',
        `accounting_type_description` varchar(255) COMMENT 'وصف نوع المحاسبة',
        `container` varchar(10) COMMENT 'الوعاء',
        `container_name` varchar(255) COMMENT 'اسم الوعاء',
        `period` varchar(20) COMMENT 'الفترة',
        `dispute_stage` varchar(10) COMMENT 'مرحلة النزاع',
        `dispute_stage_description` varchar(255) COMMENT 'وصف مرحلة النزاع',
        `case_number` varchar(100) COMMENT 'رقم القضية',
        `dispute_authority` varchar(10) COMMENT 'جهة النزاع',
        `dispute_authority_name` varchar(255) COMMENT 'اسم جهة النزاع',
        `other_authority` varchar(255) COMMENT 'جهة أخرى',
        `tax_according_to_declaration` decimal(15,2) COMMENT 'الضريبة طبقاً للإقرار',
        `tax_from_last_assessment` decimal(15,2) COMMENT 'الضريبة من آخر ربط',
        `last_assessment_year` varchar(10) COMMENT 'سنة آخر ربط',
        `tax_according_to_form` decimal(15,2) COMMENT 'الضريبة طبقاً للنموذج',
        `tax_paid` decimal(15,2) COMMENT 'الضريبة المسددة',
        `tax_according_to_declaration_before_modification` decimal(15,2) COMMENT 'الضريبة طبقاً للإقرار قبل التعديل',
        `tax_from_last_assessment_before_modification` decimal(15,2) COMMENT 'الضريبة من آخر ربط قبل التعديل',
        `tax_according_to_form_before_modification` decimal(15,2) COMMENT 'الضريبة طبقاً للنموذج قبل التعديل',
        `tax_paid_before_modification` decimal(15,2) COMMENT 'الضريبة المسددة قبل التعديل',
        `expected_tax` decimal(15,2) COMMENT 'الضريبة المتوقعة',
        `tax_according_to_law` decimal(15,2) COMMENT 'الضريبة طبقاً للقانون',
        `tax_due_payment` decimal(15,2) COMMENT 'الضريبة المستحقة',
        `export_date` date NOT NULL COMMENT 'تاريخ التصدير المستخرج من اسم الملف',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإدراج في النظام',
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
        
        KEY `idx_export_date` (`export_date`),
        KEY `idx_request_number` (`request_number`),
        KEY `idx_office_code` (`office_code`),
        KEY `idx_taxpayer_name` (`taxpayer_name`),
        KEY `idx_request_status` (`request_status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
    COMMENT='جدول طلبات المحاسبة - البيانات منظمة حسب export_date';
    ";
    $pdo->exec($sql_accounting);
    echo "<p>✅ تم إنشاء جدول طلبات المحاسبة (accounting) بدون مفتاح أساسي</p>";
    
    // جدول طلبات إنهاء النزاع - بدون مفتاح أساسي id
    $sql_conflict = "
    CREATE TABLE `conflict` (
        `request_number` varchar(50) COMMENT 'رقم الطلب',
        `creation_date` datetime COMMENT 'تاريخ الإنشاء',
        `created_by` varchar(100) COMMENT 'منشئ الطلب',
        `modification_date` datetime COMMENT 'تاريخ التعديل',
        `modified_by` varchar(100) COMMENT 'معدل الطلب',
        `request_status` varchar(10) COMMENT 'حالة الطلب',
        `status_description` varchar(255) COMMENT 'وصف الحالة',
        `request_type` varchar(10) COMMENT 'نوع الطلب',
        `request_type_name` varchar(255) COMMENT 'اسم نوع الطلب',
        `tax_registration_number` varchar(50) COMMENT 'رقم التسجيل الضريبي',
        `office_code` varchar(10) COMMENT 'كود المأمورية',
        `office_name` varchar(255) COMMENT 'اسم المأمورية',
        `taxpayer_name` varchar(255) COMMENT 'اسم المكلف',
        `address` text COMMENT 'العنوان',
        `phone_number` varchar(50) COMMENT 'رقم الهاتف',
        `email` varchar(255) COMMENT 'البريد الإلكتروني',
        `accounting_type` varchar(10) COMMENT 'نوع المحاسبة',
        `accounting_type_description` varchar(255) COMMENT 'وصف نوع المحاسبة',
        `container` varchar(10) COMMENT 'الوعاء',
        `container_name` varchar(255) COMMENT 'اسم الوعاء',
        `period` varchar(20) COMMENT 'الفترة',
        `dispute_stage` varchar(10) COMMENT 'مرحلة النزاع',
        `dispute_stage_description` varchar(255) COMMENT 'وصف مرحلة النزاع',
        `case_number` varchar(100) COMMENT 'رقم القضية',
        `dispute_authority` varchar(10) COMMENT 'جهة النزاع',
        `dispute_authority_name` varchar(255) COMMENT 'اسم جهة النزاع',
        `other_authority` varchar(255) COMMENT 'جهة أخرى',
        `tax_according_to_declaration` decimal(15,2) COMMENT 'الضريبة طبقاً للإقرار',
        `tax_from_last_assessment` decimal(15,2) COMMENT 'الضريبة من آخر ربط',
        `last_assessment_year` varchar(10) COMMENT 'سنة آخر ربط',
        `tax_according_to_form` decimal(15,2) COMMENT 'الضريبة طبقاً للنموذج',
        `tax_paid` decimal(15,2) COMMENT 'الضريبة المسددة',
        `tax_according_to_declaration_before_modification` decimal(15,2) COMMENT 'الضريبة طبقاً للإقرار قبل التعديل',
        `tax_from_last_assessment_before_modification` decimal(15,2) COMMENT 'الضريبة من آخر ربط قبل التعديل',
        `tax_according_to_form_before_modification` decimal(15,2) COMMENT 'الضريبة طبقاً للنموذج قبل التعديل',
        `tax_paid_before_modification` decimal(15,2) COMMENT 'الضريبة المسددة قبل التعديل',
        `expected_tax` decimal(15,2) COMMENT 'الضريبة المتوقعة',
        `tax_according_to_law` decimal(15,2) COMMENT 'الضريبة طبقاً للقانون',
        `tax_due_payment` decimal(15,2) COMMENT 'الضريبة المستحقة',
        `export_date` date NOT NULL COMMENT 'تاريخ التصدير المستخرج من اسم الملف',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإدراج في النظام',
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
        
        KEY `idx_export_date` (`export_date`),
        KEY `idx_request_number` (`request_number`),
        KEY `idx_office_code` (`office_code`),
        KEY `idx_taxpayer_name` (`taxpayer_name`),
        KEY `idx_request_status` (`request_status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
    COMMENT='جدول طلبات إنهاء النزاع - البيانات منظمة حسب export_date';
    ";
    $pdo->exec($sql_conflict);
    echo "<p>✅ تم إنشاء جدول طلبات إنهاء النزاع (conflict) بدون مفتاح أساسي</p>";
    
    // جدول طلبات تسوية النزاع - بدون مفتاح أساسي id
    $sql_dispute = "
    CREATE TABLE `dispute` (
        `request_number` varchar(50) COMMENT 'رقم الطلب',
        `creation_date` datetime COMMENT 'تاريخ الإنشاء',
        `created_by` varchar(100) COMMENT 'منشئ الطلب',
        `modification_date` datetime COMMENT 'تاريخ التعديل',
        `modified_by` varchar(100) COMMENT 'معدل الطلب',
        `request_status` varchar(10) COMMENT 'حالة الطلب',
        `status_description` varchar(255) COMMENT 'وصف الحالة',
        `request_type` varchar(10) COMMENT 'نوع الطلب',
        `request_type_name` varchar(255) COMMENT 'اسم نوع الطلب',
        `tax_registration_number` varchar(50) COMMENT 'رقم التسجيل الضريبي',
        `office_code` varchar(10) COMMENT 'كود المأمورية',
        `office_name` varchar(255) COMMENT 'اسم المأمورية',
        `taxpayer_name` varchar(255) COMMENT 'اسم المكلف',
        `address` text COMMENT 'العنوان',
        `phone_number` varchar(50) COMMENT 'رقم الهاتف',
        `email` varchar(255) COMMENT 'البريد الإلكتروني',
        `accounting_type` varchar(10) COMMENT 'نوع المحاسبة',
        `accounting_type_description` varchar(255) COMMENT 'وصف نوع المحاسبة',
        `container` varchar(10) COMMENT 'الوعاء',
        `container_name` varchar(255) COMMENT 'اسم الوعاء',
        `period` varchar(20) COMMENT 'الفترة',
        `dispute_stage` varchar(10) COMMENT 'مرحلة النزاع',
        `dispute_stage_description` varchar(255) COMMENT 'وصف مرحلة النزاع',
        `case_number` varchar(100) COMMENT 'رقم القضية',
        `dispute_authority` varchar(10) COMMENT 'جهة النزاع',
        `dispute_authority_name` varchar(255) COMMENT 'اسم جهة النزاع',
        `other_authority` varchar(255) COMMENT 'جهة أخرى',
        `tax_according_to_declaration` decimal(15,2) COMMENT 'الضريبة طبقاً للإقرار',
        `tax_from_last_assessment` decimal(15,2) COMMENT 'الضريبة من آخر ربط',
        `last_assessment_year` varchar(10) COMMENT 'سنة آخر ربط',
        `tax_according_to_form` decimal(15,2) COMMENT 'الضريبة طبقاً للنموذج',
        `tax_paid` decimal(15,2) COMMENT 'الضريبة المسددة',
        `tax_according_to_declaration_before_modification` decimal(15,2) COMMENT 'الضريبة طبقاً للإقرار قبل التعديل',
        `tax_from_last_assessment_before_modification` decimal(15,2) COMMENT 'الضريبة من آخر ربط قبل التعديل',
        `tax_according_to_form_before_modification` decimal(15,2) COMMENT 'الضريبة طبقاً للنموذج قبل التعديل',
        `tax_paid_before_modification` decimal(15,2) COMMENT 'الضريبة المسددة قبل التعديل',
        `expected_tax` decimal(15,2) COMMENT 'الضريبة المتوقعة',
        `tax_according_to_law` decimal(15,2) COMMENT 'الضريبة طبقاً للقانون',
        `tax_due_payment` decimal(15,2) COMMENT 'الضريبة المستحقة',
        `export_date` date NOT NULL COMMENT 'تاريخ التصدير المستخرج من اسم الملف',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإدراج في النظام',
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
        
        KEY `idx_export_date` (`export_date`),
        KEY `idx_request_number` (`request_number`),
        KEY `idx_office_code` (`office_code`),
        KEY `idx_taxpayer_name` (`taxpayer_name`),
        KEY `idx_request_status` (`request_status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
    COMMENT='جدول طلبات تسوية النزاع - البيانات منظمة حسب export_date';
    ";
    $pdo->exec($sql_dispute);
    echo "<p>✅ تم إنشاء جدول طلبات تسوية النزاع (dispute) بدون مفتاح أساسي</p>";
    
    // ===== عرض ملخص النتائج =====
    echo "<h3>📊 ملخص النتائج</h3>";
    
    // عدد السجلات في كل جدول (يجب أن يكون صفر بعد الإنشاء الجديد)
    $tables_info = [
        'accounting' => 'طلبات المحاسبة',
        'conflict' => 'طلبات إنهاء النزاع', 
        'dispute' => 'طلبات تسوية النزاع'
    ];
    
    foreach ($tables_info as $table => $arabic_name) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
            $count = $stmt->fetchColumn();
            echo "<p>📋 جدول $arabic_name ($table): $count سجل</p>";
            
            // عرض هيكل الجدول
            $stmt = $pdo->query("DESCRIBE `$table`");
            $columns = $stmt->fetchAll();
            echo "<p>🔧 عدد الحقول: " . count($columns) . " حقل</p>";
        } catch (Exception $e) {
            echo "<p>❌ خطأ في جدول $table: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>🎉 تم إعادة إنشاء الجداول الأساسية بنجاح!</h4>";
    echo "<ul>";
    echo "<li>✅ تم إزالة المفتاح الأساسي id من جميع الجداول</li>";
    echo "<li>✅ البيانات الآن منظمة حسب export_date المستخرج من اسم الملف</li>";
    echo "<li>✅ تم إضافة فهارس محسنة للبحث والاستعلام</li>";
    echo "<li>✅ تم إضافة تعليقات توضيحية لجميع الحقول</li>";
    echo "<li>✅ الجداول جاهزة لاستيراد البيانات من ملفات CSV</li>";
    echo "</ul>";
    echo "<p><strong>ملاحظة:</strong> أسماء الملفات يجب أن تكون بالتنسيق: TableNameEXPORT_YYYYMMDD.csv</p>";
    echo "<p><strong>مثال:</strong> AccountingEXPORT_20250526.csv</p>";
    echo "</div>";
    
    echo "<p><a href='index.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة للصفحة الرئيسية</a></p>";
    echo "<p><a href='import_data.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>استيراد البيانات</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    die();
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
    die();
}
?>
