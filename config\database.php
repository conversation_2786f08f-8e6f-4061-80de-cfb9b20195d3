<?php
/**
 * إعدادات قاعدة البيانات
 * TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'tiaf_db');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام
define('APP_NAME', 'TIaF Report System');
define('APP_VERSION', '1.0');
define('APP_LANGUAGE', 'ar');
define('APP_TIMEZONE', 'Africa/Cairo');

// إعدادات العرض
define('RECORDS_PER_PAGE', 50);
define('DATE_FORMAT', 'd-m-Y');
define('DATETIME_FORMAT', 'd-m-Y H:i:s');

// مسارات النظام
define('BASE_PATH', dirname(__DIR__));
define('ASSETS_PATH', BASE_PATH . '/assets');
define('INCLUDES_PATH', BASE_PATH . '/includes');
define('REPORTS_PATH', BASE_PATH . '/reports');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('UPLOAD_MAX_SIZE', 50 * 1024 * 1024); // 50 ميجابايت

// أنواع الملفات المسموحة
define('ALLOWED_FILE_TYPES', ['csv', 'xlsx', 'xls']);

// إعدادات البريد الإلكتروني (للإشعارات المستقبلية)
define('MAIL_HOST', 'localhost');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '');
define('MAIL_PASSWORD', '');
define('MAIL_FROM_EMAIL', '<EMAIL>');
define('MAIL_FROM_NAME', 'TIaF Report System');

// إعدادات التصدير
define('EXPORT_FORMATS', ['pdf', 'excel', 'csv']);
define('TEMP_DIR', BASE_PATH . '/temp');

// إعدادات الألوان (للتقارير والرسوم البيانية)
define('CHART_COLORS', [
    '#667eea', '#764ba2', '#f093fb', '#f5576c',
    '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
    '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
]);

// حالات الطلبات
define('REQUEST_STATUSES', [
    '20' => 'قيد المراجعة',
    '21' => 'مطلوب التوجه للمأمورية / اللجنة المختصة',
    '22' => 'جاري نظر النزاع',
    '30' => 'قيد الاعتماد',
    '40' => 'تم قبول الطلب',
    '50' => 'تم رفض الطلب'
]);

// أنواع الوعاء
define('CONTAINER_TYPES', [
    '1' => 'الدخل',
    '2' => 'القيمة المضافة',
    '3' => 'المرتبات',
    '4' => 'الدمغة',
    '7' => 'تصرفات عقارية'
]);

// أنواع التصنيف
define('CLASSIFICATION_TYPES', [
    '1' => 'تقديري',
    '2' => 'حسابات'
]);

// دالة للحصول على اتصال قاعدة البيانات
function getDBConnection() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $pdo = new PDO($dsn, DB_USERNAME, DB_PASSWORD, $options);
            
            // تعيين المنطقة الزمنية
            $pdo->exec("SET time_zone = '+02:00'");
            
        } catch (PDOException $e) {
            error_log("Database connection error: " . $e->getMessage());
            die("خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة لاحقاً.");
        }
    }
    
    return $pdo;
}

// دالة لتنظيف البيانات
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// دالة لتنسيق التاريخ
function formatDate($date, $format = DATE_FORMAT) {
    if (empty($date)) return '';
    
    if ($date instanceof DateTime) {
        return $date->format($format);
    }
    
    $dateTime = DateTime::createFromFormat('Y-m-d', $date);
    if ($dateTime === false) {
        $dateTime = DateTime::createFromFormat('Y-m-d H:i:s', $date);
    }
    
    return $dateTime ? $dateTime->format($format) : $date;
}

// دالة لتنسيق الأرقام
function formatNumber($number, $decimals = 2) {
    if (!is_numeric($number)) return '0.00';
    return number_format($number, $decimals, '.', ',');
}

// دالة للحصول على إعدادات النظام
function getSystemSetting($key, $default = null) {
    static $settings = null;
    
    if ($settings === null) {
        try {
            $pdo = getDBConnection();
            $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings");
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        } catch (Exception $e) {
            $settings = [];
        }
    }
    
    return isset($settings[$key]) ? $settings[$key] : $default;
}

// تعيين المنطقة الزمنية
date_default_timezone_set(APP_TIMEZONE);

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
