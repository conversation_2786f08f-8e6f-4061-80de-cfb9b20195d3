<?php
/**
 * استيراد الملفات الكبيرة - TIaF Report System
 * حل مشكلة POST Content-Length للملفات الكبيرة
 * 
 * <AUTHOR> Development Team
 * @version 1.4.6
 * @date 2025-06-17
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

// زيادة حدود PHP للملفات الكبيرة
ini_set('memory_limit', '512M');
ini_set('max_execution_time', 300); // 5 دقائق
ini_set('post_max_size', '100M');
ini_set('upload_max_filesize', '100M');

// دالة تحويل أسماء الأعمدة
function getColumnMapping() {
    return [
        'رقم الطلب' => 'request_number',
        'تاريخ الإنشاء' => 'creation_date',
        'تاريخ انشاء الحالة' => 'status_creation_date',
        'تاريخ إنشاء الحالة' => 'status_creation_date',
        'منشئ الطلب' => 'created_by',
        'تاريخ التعديل' => 'modification_date',
        'معدل الطلب' => 'modified_by',
        'حالة الطلب' => 'request_status',
        'وصف الحالة' => 'status_description',
        'نوع الطلب' => 'request_type',
        'اسم نوع الطلب' => 'request_type_name',
        'رقم التسجيل الضريبي' => 'tax_registration_number',
        'كود المأمورية' => 'office_code',
        'اسم المأمورية' => 'office_name',
        'اسم المكلف' => 'taxpayer_name',
        'العنوان' => 'address',
        'رقم الهاتف' => 'phone_number',
        'البريد الإلكتروني' => 'email',
        'نوع المحاسبة' => 'accounting_type',
        'وصف نوع المحاسبة' => 'accounting_type_description',
        'الوعاء' => 'container',
        'اسم الوعاء' => 'container_name',
        'الفترة' => 'period',
        'مرحلة النزاع' => 'dispute_stage',
        'وصف مرحلة النزاع' => 'dispute_stage_description',
        'رقم القضية' => 'case_number',
        'جهة النزاع' => 'dispute_authority',
        'اسم جهة النزاع' => 'dispute_authority_name',
        'جهة أخرى' => 'other_authority',
        'الضريبة طبقاً للإقرار' => 'tax_according_to_declaration',
        'الضريبة من آخر ربط' => 'tax_from_last_assessment',
        'سنة آخر ربط' => 'last_assessment_year',
        'الضريبة طبقاً للنموذج' => 'tax_according_to_form',
        'الضريبة المسددة' => 'tax_paid',
        'الضريبة طبقاً للإقرار قبل التعديل' => 'tax_according_to_declaration_before_modification',
        'الضريبة من آخر ربط قبل التعديل' => 'tax_from_last_assessment_before_modification',
        'الضريبة طبقاً للنموذج قبل التعديل' => 'tax_according_to_form_before_modification',
        'الضريبة المسددة قبل التعديل' => 'tax_paid_before_modification',
        'الضريبة المتوقعة' => 'expected_tax',
        'الضريبة طبقاً للقانون' => 'tax_according_to_law',
        'الضريبة المستحقة' => 'tax_due_payment'
    ];
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استيراد الملفات الكبيرة - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            cursor: pointer;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .progress-container {
            background: #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 30px;
            background: #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .file-input {
            width: 100%;
            padding: 15px;
            border: 2px dashed #667eea;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
            cursor: pointer;
        }
        
        .file-input:hover {
            border-color: #5a6fd8;
            background: #f8f9ff;
        }
        
        .batch-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📁 استيراد الملفات الكبيرة</h1>
            <p>استيراد ملفات CSV الكبيرة (حتى 100 ميجابايت) بمعالجة متقدمة</p>
        </div>
        
        <div class="content">
            <?php
            // التحقق من مشكلة POST Content-Length
            if (isset($_SERVER['CONTENT_LENGTH']) && empty($_POST) && empty($_FILES)) {
                $postMaxSize = ini_get('post_max_size');
                $uploadMaxSize = ini_get('upload_max_filesize');
                $contentLength = $_SERVER['CONTENT_LENGTH'];
                
                echo "<div class='section error'>";
                echo "<h3>❌ حجم الملف كبير جداً</h3>";
                echo "<p><strong>حجم الملف المرسل:</strong> " . number_format($contentLength / 1024 / 1024, 2) . " ميجابايت</p>";
                echo "<p><strong>الحد الأقصى المسموح:</strong> $postMaxSize</p>";
                echo "<p><strong>الحد الأقصى للرفع:</strong> $uploadMaxSize</p>";
                echo "<h4>الحلول المقترحة:</h4>";
                echo "<ol>";
                echo "<li><strong>قسم الملف:</strong> استخدم أداة تقسيم CSV أدناه</li>";
                echo "<li><strong>ضغط الملف:</strong> احفظ الملف بتنسيق مضغوط</li>";
                echo "<li><strong>إزالة الأعمدة غير المطلوبة:</strong> احذف الأعمدة الفارغة</li>";
                echo "</ol>";
                echo "</div>";
            }
            
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
                $table = $_POST['import_table'];
                $batchSize = intval($_POST['batch_size'] ?? 1000);
                $clearTable = isset($_POST['clear_table']);
                $file = $_FILES['csv_file'];
                
                if ($file['error'] === UPLOAD_ERR_OK) {
                    try {
                        $pdo = getDBConnection();
                        
                        echo "<div class='section'>";
                        echo "<h3>🔄 بدء عملية الاستيراد المتقدمة</h3>";
                        echo "<p><strong>الجدول:</strong> $table</p>";
                        echo "<p><strong>الملف:</strong> " . htmlspecialchars($file['name']) . "</p>";
                        echo "<p><strong>الحجم:</strong> " . number_format($file['size'] / 1024 / 1024, 2) . " ميجابايت</p>";
                        echo "<p><strong>حجم الدفعة:</strong> $batchSize سجل</p>";
                        
                        // استخراج تاريخ التصدير
                        $exportDate = extractExportDate($file['name']);
                        echo "<p><strong>تاريخ التصدير:</strong> $exportDate</p>";
                        
                        // حذف البيانات السابقة
                        if ($clearTable) {
                            $pdo->exec("DELETE FROM `$table`");
                            echo "<p>🗑️ تم حذف جميع البيانات السابقة</p>";
                        } else {
                            $stmt = $pdo->prepare("DELETE FROM `$table` WHERE export_date = ?");
                            $stmt->execute([$exportDate]);
                            $deletedRows = $stmt->rowCount();
                            echo "<p>🗑️ تم حذف $deletedRows سجل لتاريخ $exportDate</p>";
                        }
                        
                        echo "</div>";
                        
                        // معالجة الملف بدفعات
                        echo "<div class='progress-container'>";
                        echo "<h3>📊 تقدم الاستيراد</h3>";
                        echo "<div class='progress-bar'>";
                        echo "<div class='progress-fill' id='progressBar'>0%</div>";
                        echo "</div>";
                        echo "<div id='progressInfo'>جاري التحضير...</div>";
                        echo "</div>";
                        
                        // فتح الملف
                        $handle = fopen($file['tmp_name'], 'r');
                        
                        // قراءة العناوين
                        $headers = fgetcsv($handle);
                        $cleanHeaders = array_map(function($header) {
                            return trim(str_replace("\xEF\xBB\xBF", '', $header));
                        }, $headers);
                        
                        // تحويل أسماء الأعمدة
                        $columnMapping = getColumnMapping();
                        $mappedHeaders = [];
                        foreach ($cleanHeaders as $header) {
                            $mappedHeaders[] = $columnMapping[$header] ?? $header;
                        }
                        
                        // إعداد الاستعلام
                        $columns = implode('`, `', $mappedHeaders);
                        $placeholders = str_repeat('?,', count($mappedHeaders)) . '?';
                        $placeholders = rtrim($placeholders, ',');
                        $sql = "INSERT INTO `$table` (`$columns`, `export_date`) VALUES ($placeholders)";
                        $stmt = $pdo->prepare($sql);
                        
                        // حساب إجمالي الأسطر
                        $totalLines = 0;
                        while (fgetcsv($handle) !== FALSE) {
                            $totalLines++;
                        }
                        rewind($handle);
                        fgetcsv($handle); // تخطي العناوين
                        
                        echo "<script>";
                        echo "document.getElementById('progressInfo').innerHTML = 'إجمالي السجلات: " . number_format($totalLines) . "';";
                        echo "</script>";
                        
                        // معالجة البيانات بدفعات
                        $successCount = 0;
                        $errorCount = 0;
                        $processedLines = 0;
                        $batch = [];
                        
                        while (($data = fgetcsv($handle)) !== FALSE) {
                            $processedLines++;
                            
                            if (count($data) === count($mappedHeaders)) {
                                $data[] = $exportDate;
                                $batch[] = $data;
                                
                                // معالجة الدفعة عند الوصول للحد المطلوب
                                if (count($batch) >= $batchSize) {
                                    $pdo->beginTransaction();
                                    try {
                                        foreach ($batch as $row) {
                                            $stmt->execute($row);
                                            $successCount++;
                                        }
                                        $pdo->commit();
                                    } catch (Exception $e) {
                                        $pdo->rollback();
                                        $errorCount += count($batch);
                                    }
                                    $batch = [];
                                    
                                    // تحديث شريط التقدم
                                    $progress = round(($processedLines / $totalLines) * 100);
                                    echo "<script>";
                                    echo "document.getElementById('progressBar').style.width = '$progress%';";
                                    echo "document.getElementById('progressBar').innerHTML = '$progress%';";
                                    echo "document.getElementById('progressInfo').innerHTML = 'تم معالجة: " . number_format($processedLines) . " من " . number_format($totalLines) . " سجل';";
                                    echo "</script>";
                                    
                                    // إرسال البيانات للمتصفح
                                    if (ob_get_level()) {
                                        ob_flush();
                                    }
                                    flush();
                                }
                            } else {
                                $errorCount++;
                            }
                        }
                        
                        // معالجة الدفعة الأخيرة
                        if (!empty($batch)) {
                            $pdo->beginTransaction();
                            try {
                                foreach ($batch as $row) {
                                    $stmt->execute($row);
                                    $successCount++;
                                }
                                $pdo->commit();
                            } catch (Exception $e) {
                                $pdo->rollback();
                                $errorCount += count($batch);
                            }
                        }
                        
                        fclose($handle);
                        
                        // النتائج النهائية
                        echo "<script>";
                        echo "document.getElementById('progressBar').style.width = '100%';";
                        echo "document.getElementById('progressBar').innerHTML = '100%';";
                        echo "</script>";
                        
                        echo "<div class='section success'>";
                        echo "<h3>🎉 تم الانتهاء من الاستيراد!</h3>";
                        echo "<p><strong>إجمالي السجلات:</strong> " . number_format($totalLines) . "</p>";
                        echo "<p><strong>تم بنجاح:</strong> " . number_format($successCount) . "</p>";
                        echo "<p><strong>أخطاء:</strong> " . number_format($errorCount) . "</p>";
                        echo "<p><strong>معدل النجاح:</strong> " . round(($successCount / $totalLines) * 100, 2) . "%</p>";
                        echo "</div>";
                        
                    } catch (Exception $e) {
                        echo "<div class='section error'>";
                        echo "<h3>❌ خطأ في العملية</h3>";
                        echo "<p>" . $e->getMessage() . "</p>";
                        echo "</div>";
                    }
                } else {
                    echo "<div class='section error'>";
                    echo "<h3>❌ خطأ في رفع الملف</h3>";
                    echo "<p>كود الخطأ: " . $file['error'] . "</p>";
                    echo "</div>";
                }
            }
            ?>
            
            <!-- معلومات الحدود الحالية -->
            <div class="section">
                <h3>⚙️ إعدادات النظام الحالية</h3>
                <div class="batch-info">
                    <p><strong>الحد الأقصى لحجم POST:</strong> <?php echo ini_get('post_max_size'); ?></p>
                    <p><strong>الحد الأقصى لرفع الملفات:</strong> <?php echo ini_get('upload_max_filesize'); ?></p>
                    <p><strong>الحد الأقصى للذاكرة:</strong> <?php echo ini_get('memory_limit'); ?></p>
                    <p><strong>الحد الأقصى لوقت التنفيذ:</strong> <?php echo ini_get('max_execution_time'); ?> ثانية</p>
                </div>
            </div>
            
            <!-- نماذج الاستيراد -->
            <div class="section">
                <h3>📥 استيراد ملف كبير</h3>
                <form method="post" enctype="multipart/form-data">
                    <div>
                        <label><strong>اختر الجدول:</strong></label>
                        <select name="import_table" required style="width: 100%; padding: 10px; margin: 10px 0;">
                            <option value="accounting">جدول المحاسبة (Accounting)</option>
                            <option value="conflict">جدول إنهاء النزاع (Conflict)</option>
                            <option value="dispute">جدول تسوية النزاع (Dispute)</option>
                        </select>
                    </div>
                    
                    <div>
                        <label><strong>حجم الدفعة:</strong></label>
                        <select name="batch_size" style="width: 100%; padding: 10px; margin: 10px 0;">
                            <option value="500">500 سجل (للملفات الكبيرة جداً)</option>
                            <option value="1000" selected>1000 سجل (موصى به)</option>
                            <option value="2000">2000 سجل (للملفات المتوسطة)</option>
                            <option value="5000">5000 سجل (للملفات الصغيرة)</option>
                        </select>
                    </div>
                    
                    <div class="file-input">
                        <input type="file" name="csv_file" accept=".csv" required>
                        <p>اختر ملف CSV (حتى 100 ميجابايت)</p>
                    </div>
                    
                    <div style="margin: 10px 0;">
                        <label style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" name="clear_table" value="1">
                            <span>حذف جميع البيانات السابقة من الجدول قبل الاستيراد</span>
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-success">🚀 بدء الاستيراد المتقدم</button>
                </form>
            </div>
            
            <!-- نصائح للملفات الكبيرة -->
            <div class="section warning">
                <h3>💡 نصائح للملفات الكبيرة</h3>
                <h4>لتحسين الأداء:</h4>
                <ul>
                    <li><strong>استخدم حجم دفعة أصغر</strong> للملفات الكبيرة جداً (500-1000 سجل)</li>
                    <li><strong>تأكد من استقرار الاتصال</strong> بالإنترنت أثناء الرفع</li>
                    <li><strong>أغلق التطبيقات الأخرى</strong> لتوفير الذاكرة</li>
                    <li><strong>استخدم متصفح حديث</strong> يدعم رفع الملفات الكبيرة</li>
                </ul>
                
                <h4>إذا فشل الاستيراد:</h4>
                <ul>
                    <li><strong>قسم الملف:</strong> استخدم أدوات تقسيم CSV</li>
                    <li><strong>نظف البيانات:</strong> احذف الأعمدة والصفوف الفارغة</li>
                    <li><strong>ضغط الملف:</strong> احفظ بتنسيق مضغوط</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="import_data.php" class="btn">الاستيراد العادي</a>
                <a href="dashboard.php" class="btn btn-success">عرض لوحة التحكم</a>
            </div>
        </div>
    </div>
    
    <script>
        // تحسين واجهة رفع الملفات
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.querySelector('input[type="file"]');
            const fileInputDiv = document.querySelector('.file-input');
            
            fileInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    const file = this.files[0];
                    const sizeMB = (file.size / 1024 / 1024).toFixed(2);
                    fileInputDiv.innerHTML = `
                        <input type="file" name="csv_file" accept=".csv" required style="display: none;">
                        <p><strong>الملف المحدد:</strong> ${file.name}</p>
                        <p><strong>الحجم:</strong> ${sizeMB} ميجابايت</p>
                        <button type="button" onclick="this.parentElement.innerHTML='<input type=\\'file\\' name=\\'csv_file\\' accept=\\'.csv\\' required><p>اختر ملف CSV (حتى 100 ميجابايت)</p>'; location.reload();" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px;">تغيير الملف</button>
                    `;
                }
            });
        });
    </script>
</body>
</html>
